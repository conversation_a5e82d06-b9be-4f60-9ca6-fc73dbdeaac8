#workspace location
wrk_path = r'C:\Users\<USER>\Documents\Internal\Standard\DataLoading'
sourcesPath = wrk_path + '\\Sources'
logPath = wrk_path + '\\Loadlog'
inputPath = wrk_path + '\\Input'

#SQL database connection
SQLServer = r'vwmazpssql\SQL17'
SQLPort = ''
SQLDatabase = 'ETLConfiguration_PEAPI'
Trusted_Connection = 'yes' # windows authentication will be used for connecting to SQLServer database if Trusted_Connection set to yes
SQLUserName = '' 
SQLpassword_encrypted = b''
SQLpassword_encryptKey = b''

# Snowflake connection parameters
SnowflakeAccount = 'FISDEV.us-east-1'  # Format: orgname-accountname (e.g., xy12345.us-east-1)
SnowflakeWarehouse = 'CAPITAL_MARKETS_IDR__WH'
SnowflakeDatabase = 'ETLCONFIGURATION_PEAPI'
SnowflakeSchema = 'LANDING'
SnowflakeRole = 'SERV_RL__SERVICE_IDR_REPORTING'

# Snowflake Authentication
SnowflakeUsername = 'SERVICE_IDR_REPORTING'
SnowflakePassword = '**************'  # Store encrypted or use key vault
SnowflakePassword_encrypted = b''
SnowflakePassword_encryptKey = b''

# Optional
SnowflakeRegion = 'us-east-1'  # Your Snowflake region

#ExtractionStartTime setting: number of hours before last successful JobHistory load, which will be used to update ExtractionStartTime once load completed
nb_hrs = 2
