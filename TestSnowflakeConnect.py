import snowflake.connector

conn = snowflake.connector.connect(
    user='SERVICE_IDR_REPORTING',
    password='JaftarbH<PERSON>@516',    
    account='FISDEV.us-east-1',
    warehouse='CAPITAL_MARKETS_IDR__WH',
    database='ETLCONFIGURATION_PEAPI',
    schema='LANDING',
    role='SERV_RL__SERVICE_IDR_REPORTING'

)

cursor = conn.cursor()
cursor.execute("SELECT CURRENT_ACCOUNT(), CURRENT_USER(), CURRENT_WAREHOUSE(), CURRENT_DATABASE(), CURRENT_SCHEMA(), CURRENT_ROLE()")
results = cursor.fetchall()
for row in results:
    print(row)

cursor.close()
conn.close()