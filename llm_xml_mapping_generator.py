"""
LLM-Powered XML Mapping Generator
Generates XML mapping files based on natural language prompts
"""

import openai
import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional
import yaml
from dataclasses import dataclass
from pathlib import Path
import logging

@dataclass
class MappingRequest:
    """Structure for mapping generation requests"""
    user_prompt: str
    source_schema: str
    target_schema: str
    transformation_rules: List[str]
    business_context: str

@dataclass
class XMLMapping:
    """Generated XML mapping structure"""
    mapping_id: str
    source_elements: List[str]
    target_elements: List[str]
    transformations: Dict[str, str]
    xml_content: str

class LLMXMLMappingGenerator:
    """Main class for LLM-powered XML mapping generation"""
    
    def __init__(self, config_path: str = "mapping_config.yaml"):
        self.config = self._load_config(config_path)
        self.template_library = TemplateLibrary(self.config['template_path'])
        self.schema_validator = SchemaValidator(self.config['schema_path'])
        self.llm_client = self._initialize_llm()
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        with open(config_path, 'r') as file:
            return yaml.safe_load(file)
    
    def _initialize_llm(self):
        """Initialize LLM client (OpenAI, Azure OpenAI, or local model)"""
        if self.config['llm_provider'] == 'openai':
            openai.api_key = self.config['openai_api_key']
            return openai
        elif self.config['llm_provider'] == 'azure':
            # Configure Azure OpenAI
            openai.api_type = "azure"
            openai.api_base = self.config['azure_endpoint']
            openai.api_version = self.config['azure_api_version']
            openai.api_key = self.config['azure_api_key']
            return openai
        else:
            raise ValueError(f"Unsupported LLM provider: {self.config['llm_provider']}")
    
    def generate_mapping(self, request: MappingRequest) -> XMLMapping:
        """Generate XML mapping based on user prompt"""
        try:
            # Step 1: Analyze user prompt and extract requirements
            requirements = self._analyze_prompt(request)
            
            # Step 2: Find relevant templates
            templates = self.template_library.find_matching_templates(requirements)
            
            # Step 3: Generate mapping using LLM
            mapping_spec = self._generate_mapping_spec(request, templates, requirements)
            
            # Step 4: Create XML mapping file
            xml_mapping = self._create_xml_mapping(mapping_spec)
            
            # Step 5: Validate generated mapping
            validation_result = self.schema_validator.validate_mapping(xml_mapping)
            
            if not validation_result.is_valid:
                # Attempt to fix issues using LLM
                xml_mapping = self._fix_mapping_issues(xml_mapping, validation_result)
            
            return xml_mapping
            
        except Exception as e:
            self.logger.error(f"Error generating mapping: {str(e)}")
            raise
    
    def _analyze_prompt(self, request: MappingRequest) -> Dict:
        """Use LLM to analyze user prompt and extract structured requirements"""
        
        analysis_prompt = f"""
        Analyze the following data mapping request and extract structured requirements:
        
        User Prompt: {request.user_prompt}
        Source Schema: {request.source_schema}
        Target Schema: {request.target_schema}
        Business Context: {request.business_context}
        
        Extract and return a JSON structure with:
        1. source_fields: List of source data fields mentioned
        2. target_fields: List of target data fields mentioned
        3. transformations: List of data transformations required
        4. business_rules: List of business rules to apply
        5. data_types: Expected data types for fields
        6. validation_rules: Data validation requirements
        7. mapping_complexity: Simple/Medium/Complex
        
        Return only valid JSON.
        """
        
        response = self.llm_client.ChatCompletion.create(
            model=self.config['llm_model'],
            messages=[
                {"role": "system", "content": "You are an expert data integration analyst."},
                {"role": "user", "content": analysis_prompt}
            ],
            temperature=0.1
        )
        
        try:
            return json.loads(response.choices[0].message.content)
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse LLM response as JSON, using fallback")
            return self._fallback_analysis(request)
    
    def _generate_mapping_spec(self, request: MappingRequest, templates: List[Dict], requirements: Dict) -> Dict:
        """Generate detailed mapping specification using LLM"""
        
        template_context = "\n".join([f"Template {i+1}: {t['description']}" for i, t in enumerate(templates)])
        
        mapping_prompt = f"""
        Generate a detailed XML mapping specification based on:
        
        Requirements: {json.dumps(requirements, indent=2)}
        Available Templates: {template_context}
        
        Create a mapping specification that includes:
        1. Element mappings (source -> target)
        2. Data transformations
        3. Conditional logic
        4. Error handling
        5. Validation rules
        
        Format as JSON with the following structure:
        {{
            "mapping_id": "unique_identifier",
            "mappings": [
                {{
                    "source_path": "xpath_to_source",
                    "target_path": "xpath_to_target",
                    "transformation": "transformation_function",
                    "conditions": ["condition1", "condition2"],
                    "default_value": "default_if_null"
                }}
            ],
            "global_rules": ["rule1", "rule2"],
            "error_handling": "strategy"
        }}
        """
        
        response = self.llm_client.ChatCompletion.create(
            model=self.config['llm_model'],
            messages=[
                {"role": "system", "content": "You are an expert XML mapping specialist."},
                {"role": "user", "content": mapping_prompt}
            ],
            temperature=0.2
        )
        
        return json.loads(response.choices[0].message.content)
    
    def _create_xml_mapping(self, mapping_spec: Dict) -> XMLMapping:
        """Create actual XML mapping file from specification"""
        
        # Generate XML mapping using template engine
        xml_template = self._get_xml_template(mapping_spec)
        xml_content = self._populate_template(xml_template, mapping_spec)
        
        return XMLMapping(
            mapping_id=mapping_spec['mapping_id'],
            source_elements=[m['source_path'] for m in mapping_spec['mappings']],
            target_elements=[m['target_path'] for m in mapping_spec['mappings']],
            transformations={m['source_path']: m['transformation'] for m in mapping_spec['mappings']},
            xml_content=xml_content
        )
    
    def _get_xml_template(self, mapping_spec: Dict) -> str:
        """Get appropriate XML template based on mapping complexity"""
        complexity = mapping_spec.get('complexity', 'medium')
        return self.template_library.get_template(complexity)
    
    def _populate_template(self, template: str, mapping_spec: Dict) -> str:
        """Populate XML template with mapping specification"""
        # Use Jinja2 or similar templating engine
        from jinja2 import Template
        
        template_obj = Template(template)
        return template_obj.render(mapping_spec=mapping_spec)
    
    def _fallback_analysis(self, request: MappingRequest) -> Dict:
        """Fallback analysis when LLM parsing fails"""
        return {
            "source_fields": [],
            "target_fields": [],
            "transformations": [],
            "business_rules": [],
            "data_types": {},
            "validation_rules": [],
            "mapping_complexity": "medium"
        }

class TemplateLibrary:
    """Manages proprietary XML mapping templates"""
    
    def __init__(self, template_path: str):
        self.template_path = Path(template_path)
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict:
        """Load all XML mapping templates"""
        templates = {}
        for template_file in self.template_path.glob("*.xml"):
            template_name = template_file.stem
            with open(template_file, 'r') as f:
                templates[template_name] = {
                    'content': f.read(),
                    'metadata': self._extract_metadata(template_file)
                }
        return templates
    
    def _extract_metadata(self, template_file: Path) -> Dict:
        """Extract metadata from template file"""
        # Look for accompanying .yaml metadata file
        metadata_file = template_file.with_suffix('.yaml')
        if metadata_file.exists():
            with open(metadata_file, 'r') as f:
                return yaml.safe_load(f)
        return {}
    
    def find_matching_templates(self, requirements: Dict) -> List[Dict]:
        """Find templates that match the requirements"""
        matching_templates = []
        
        for template_name, template_data in self.templates.items():
            metadata = template_data['metadata']
            
            # Score template based on requirements match
            score = self._calculate_template_score(metadata, requirements)
            
            if score > 0.5:  # Threshold for relevance
                matching_templates.append({
                    'name': template_name,
                    'content': template_data['content'],
                    'metadata': metadata,
                    'score': score
                })
        
        # Sort by relevance score
        return sorted(matching_templates, key=lambda x: x['score'], reverse=True)
    
    def _calculate_template_score(self, metadata: Dict, requirements: Dict) -> float:
        """Calculate relevance score for template"""
        score = 0.0
        
        # Check field overlap
        template_fields = set(metadata.get('fields', []))
        required_fields = set(requirements.get('source_fields', []) + requirements.get('target_fields', []))
        
        if template_fields and required_fields:
            overlap = len(template_fields.intersection(required_fields))
            score += (overlap / len(required_fields)) * 0.4
        
        # Check complexity match
        template_complexity = metadata.get('complexity', 'medium')
        required_complexity = requirements.get('mapping_complexity', 'medium')
        
        if template_complexity == required_complexity:
            score += 0.3
        
        # Check business domain match
        template_domain = metadata.get('business_domain', '')
        if template_domain and template_domain in requirements.get('business_context', ''):
            score += 0.3
        
        return score
    
    def get_template(self, template_type: str) -> str:
        """Get specific template by type"""
        return self.templates.get(template_type, {}).get('content', '')

class SchemaValidator:
    """Validates generated XML mappings against schemas"""
    
    def __init__(self, schema_path: str):
        self.schema_path = Path(schema_path)
        self.schemas = self._load_schemas()
    
    def _load_schemas(self) -> Dict:
        """Load XML schemas for validation"""
        schemas = {}
        for schema_file in self.schema_path.glob("*.xsd"):
            schema_name = schema_file.stem
            schemas[schema_name] = str(schema_file)
        return schemas
    
    def validate_mapping(self, xml_mapping: XMLMapping) -> 'ValidationResult':
        """Validate XML mapping against schemas"""
        try:
            # Parse XML to check well-formedness
            ET.fromstring(xml_mapping.xml_content)
            
            # Additional validation logic here
            # - Check against XSD schemas
            # - Validate XPath expressions
            # - Check data type compatibility
            
            return ValidationResult(is_valid=True, errors=[])
            
        except ET.ParseError as e:
            return ValidationResult(is_valid=False, errors=[f"XML Parse Error: {str(e)}"])
        except Exception as e:
            return ValidationResult(is_valid=False, errors=[f"Validation Error: {str(e)}"])

@dataclass
class ValidationResult:
    """Result of XML mapping validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []

# Example usage and configuration
if __name__ == "__main__":
    # Example configuration
    config = {
        'llm_provider': 'openai',
        'llm_model': 'gpt-4',
        'openai_api_key': 'your-api-key',
        'template_path': './templates',
        'schema_path': './schemas'
    }
    
    # Save example config
    with open('mapping_config.yaml', 'w') as f:
        yaml.dump(config, f)
    
    # Example usage
    generator = LLMXMLMappingGenerator()
    
    request = MappingRequest(
        user_prompt="Map customer data from CRM to IDR format with address normalization",
        source_schema="crm_customer.xsd",
        target_schema="idr_customer.xsd",
        transformation_rules=["normalize_address", "validate_email"],
        business_context="Customer data integration for reporting"
    )
    
    mapping = generator.generate_mapping(request)
    print(f"Generated mapping: {mapping.mapping_id}")
