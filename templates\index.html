<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM XML Mapping Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .mapping-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        .progress-container {
            display: none;
        }
        .element-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .chat-interface {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: 2rem;
        }
        .assistant-message {
            background-color: #e9ecef;
            margin-right: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <div class="container mt-4">
                    <h1 class="mb-4">LLM XML Mapping Generator</h1>
                    
                    <!-- Mapping Request Form -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>Create XML Mapping</h5>
                        </div>
                        <div class="card-body">
                            <form id="mappingForm">
                                <div class="mb-3">
                                    <label for="prompt" class="form-label">Describe your mapping requirements:</label>
                                    <textarea class="form-control" id="prompt" rows="3" 
                                        placeholder="Example: Map customer data from CRM system to IDR format, including address normalization and email validation"></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sourceSchema" class="form-label">Source Schema:</label>
                                            <select class="form-select" id="sourceSchema">
                                                <option value="">Select source schema...</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="targetSchema" class="form-label">Target Schema:</label>
                                            <select class="form-select" id="targetSchema">
                                                <option value="">Select target schema...</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="businessContext" class="form-label">Business Context:</label>
                                    <input type="text" class="form-control" id="businessContext" 
                                        placeholder="e.g., Customer data integration, Financial reporting, etc.">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="transformationRules" class="form-label">Transformation Rules (comma-separated):</label>
                                    <input type="text" class="form-control" id="transformationRules" 
                                        placeholder="e.g., normalize_address, validate_email, format_phone">
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Generate Mapping</button>
                                <button type="button" class="btn btn-secondary" id="streamGenerate">Generate with Live Updates</button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="progress-container">
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6>Generation Progress</h6>
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="progressMessage">Starting...</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Results -->
                    <div id="results" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>Generated XML Mapping</h5>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" id="validateBtn">Validate</button>
                                    <button class="btn btn-sm btn-success" id="downloadBtn">Download</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Source Elements</h6>
                                        <div class="element-list" id="sourceElements"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Target Elements</h6>
                                        <div class="element-list" id="targetElements"></div>
                                    </div>
                                </div>
                                
                                <h6 class="mt-3">XML Mapping Preview</h6>
                                <div class="mapping-preview">
                                    <pre><code class="language-xml" id="xmlPreview"></code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-md-4">
                <div class="container mt-4">
                    <!-- Available Templates -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6>Available Templates</h6>
                        </div>
                        <div class="card-body">
                            <div id="templateList">
                                <div class="text-muted">Loading templates...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Chat Interface -->
                    <div class="card">
                        <div class="card-header">
                            <h6>AI Assistant</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="chat-interface" id="chatInterface">
                                <div class="message assistant-message">
                                    Hello! I can help you create XML mappings. Describe what you need and I'll generate the appropriate mapping files.
                                </div>
                            </div>
                            <div class="p-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="chatInput" placeholder="Ask about mapping requirements...">
                                    <button class="btn btn-outline-primary" id="chatSend">Send</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <script>
        // Initialize Socket.IO
        const socket = io();
        let currentMappingId = null;
        
        // Load schemas and templates on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSchemas();
            loadTemplates();
        });
        
        // Form submission
        document.getElementById('mappingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateMapping(false);
        });
        
        // Stream generation
        document.getElementById('streamGenerate').addEventListener('click', function() {
            generateMapping(true);
        });
        
        // Chat functionality
        document.getElementById('chatSend').addEventListener('click', sendChatMessage);
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendChatMessage();
            }
        });
        
        function generateMapping(useStream) {
            const formData = {
                prompt: document.getElementById('prompt').value,
                source_schema: document.getElementById('sourceSchema').value,
                target_schema: document.getElementById('targetSchema').value,
                business_context: document.getElementById('businessContext').value,
                transformation_rules: document.getElementById('transformationRules').value.split(',').map(s => s.trim()).filter(s => s)
            };
            
            if (!formData.prompt) {
                alert('Please enter a mapping description');
                return;
            }
            
            if (useStream) {
                generateMappingStream(formData);
            } else {
                generateMappingAPI(formData);
            }
        }
        
        function generateMappingAPI(formData) {
            fetch('/api/generate-mapping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResults(data);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating the mapping');
            });
        }
        
        function generateMappingStream(formData) {
            document.querySelector('.progress-container').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            
            socket.emit('generate_mapping_stream', formData);
        }
        
        function displayResults(data) {
            currentMappingId = data.mapping_id;
            
            // Show source elements
            const sourceList = document.getElementById('sourceElements');
            sourceList.innerHTML = data.source_elements.map(el => `<div class="badge bg-primary me-1 mb-1">${el}</div>`).join('');
            
            // Show target elements
            const targetList = document.getElementById('targetElements');
            targetList.innerHTML = data.target_elements.map(el => `<div class="badge bg-success me-1 mb-1">${el}</div>`).join('');
            
            // Show XML preview
            document.getElementById('xmlPreview').textContent = data.preview || data.xml_content;
            Prism.highlightAll();
            
            // Show results section
            document.getElementById('results').style.display = 'block';
            document.querySelector('.progress-container').style.display = 'none';
        }
        
        function loadSchemas() {
            fetch('/api/schemas')
                .then(response => response.json())
                .then(data => {
                    const sourceSelect = document.getElementById('sourceSchema');
                    const targetSelect = document.getElementById('targetSchema');
                    
                    data.schemas.forEach(schema => {
                        const option1 = new Option(schema, schema);
                        const option2 = new Option(schema, schema);
                        sourceSelect.add(option1);
                        targetSelect.add(option2);
                    });
                })
                .catch(error => console.error('Error loading schemas:', error));
        }
        
        function loadTemplates() {
            fetch('/api/templates')
                .then(response => response.json())
                .then(data => {
                    const templateList = document.getElementById('templateList');
                    templateList.innerHTML = data.templates.map(template => `
                        <div class="card mb-2">
                            <div class="card-body p-2">
                                <h6 class="card-title mb-1">${template.name}</h6>
                                <p class="card-text small">${template.description}</p>
                                <span class="badge bg-secondary">${template.complexity}</span>
                                ${template.business_domain ? `<span class="badge bg-info">${template.business_domain}</span>` : ''}
                            </div>
                        </div>
                    `).join('');
                })
                .catch(error => console.error('Error loading templates:', error));
        }
        
        function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addChatMessage(message, 'user');
            input.value = '';
            
            // Simulate AI response (in real implementation, this would call the LLM)
            setTimeout(() => {
                const response = generateAIResponse(message);
                addChatMessage(response, 'assistant');
            }, 1000);
        }
        
        function addChatMessage(message, sender) {
            const chatInterface = document.getElementById('chatInterface');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = message;
            chatInterface.appendChild(messageDiv);
            chatInterface.scrollTop = chatInterface.scrollHeight;
        }
        
        function generateAIResponse(userMessage) {
            // Simple response generation (replace with actual LLM integration)
            const responses = [
                "I can help you create that mapping. Please provide more details about the source and target schemas.",
                "That sounds like a complex transformation. Consider using templates for similar mappings.",
                "Make sure to specify the data types and validation rules for better mapping accuracy.",
                "I recommend breaking down complex mappings into smaller, manageable components."
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }
        
        // Socket.IO event handlers
        socket.on('status', function(data) {
            document.querySelector('.progress-bar').style.width = data.progress + '%';
            document.getElementById('progressMessage').textContent = data.message;
        });
        
        socket.on('mapping_complete', function(data) {
            displayResults(data);
        });
        
        socket.on('error', function(data) {
            alert('Error: ' + data.message);
            document.querySelector('.progress-container').style.display = 'none';
        });
        
        // Download functionality
        document.getElementById('downloadBtn').addEventListener('click', function() {
            if (currentMappingId) {
                window.location.href = `/api/download-mapping/${currentMappingId}`;
            }
        });
        
        // Validation functionality
        document.getElementById('validateBtn').addEventListener('click', function() {
            const xmlContent = document.getElementById('xmlPreview').textContent;
            
            fetch('/api/validate-mapping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ xml_content: xmlContent })
            })
            .then(response => response.json())
            .then(data => {
                if (data.is_valid) {
                    alert('Mapping is valid!');
                } else {
                    alert('Validation errors:\n' + data.errors.join('\n'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred during validation');
            });
        });
    </script>
</body>
</html>
