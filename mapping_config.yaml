# LLM XML Mapping Generator Configuration

# LLM Provider Configuration
llm_provider: "openai"  # Options: openai, azure, local
llm_model: "gpt-4"
openai_api_key: "your-openai-api-key-here"

# Azure OpenAI Configuration (if using Azure)
azure_endpoint: "https://your-resource.openai.azure.com/"
azure_api_version: "2023-12-01-preview"
azure_api_key: "your-azure-api-key-here"
azure_deployment_name: "gpt-4"

# Local Model Configuration (if using local LLM)
local_model_endpoint: "http://localhost:8000"
local_model_name: "llama2"

# File Paths
template_path: "./templates"
schema_path: "./schemas"
output_path: "./generated_mappings"
log_path: "./logs"

# Generation Settings
generation:
  max_tokens: 4000
  temperature: 0.2
  top_p: 0.9
  frequency_penalty: 0.0
  presence_penalty: 0.0
  
# Validation Settings
validation:
  enable_schema_validation: true
  enable_xpath_validation: true
  enable_data_type_validation: true
  max_validation_errors: 10

# Template Matching Settings
template_matching:
  similarity_threshold: 0.5
  max_templates_to_consider: 5
  field_overlap_weight: 0.4
  complexity_match_weight: 0.3
  domain_match_weight: 0.3

# Error Handling
error_handling:
  max_retries: 3
  retry_delay_seconds: 2
  fallback_to_simple_template: true
  log_all_errors: true

# Security Settings
security:
  sanitize_user_input: true
  validate_xml_structure: true
  prevent_xxe_attacks: true
  max_file_size_mb: 10

# Performance Settings
performance:
  cache_templates: true
  cache_schemas: true
  parallel_processing: true
  max_concurrent_requests: 5

# Integration Settings
integration:
  enable_webhook_notifications: false
  webhook_url: "https://your-webhook-endpoint.com"
  enable_audit_logging: true
  audit_log_retention_days: 90

# IDR Specific Settings
idr:
  default_namespace: "http://schemas.idr.com/v1.0"
  default_version: "1.0"
  enable_idr_validation: true
  idr_schema_registry_url: "https://schemas.idr.com/registry"

# Business Domain Mappings
business_domains:
  customer_management:
    - "customer"
    - "contact"
    - "account"
    - "address"
  financial_services:
    - "transaction"
    - "account"
    - "payment"
    - "investment"
  insurance:
    - "policy"
    - "claim"
    - "premium"
    - "coverage"
  healthcare:
    - "patient"
    - "provider"
    - "claim"
    - "treatment"

# Common Transformation Functions
transformation_functions:
  normalize_address:
    description: "Standardize address format"
    parameters: ["address_line", "city", "state", "zip"]
  validate_email:
    description: "Validate email format and domain"
    parameters: ["email_address"]
  format_phone:
    description: "Format phone number to standard format"
    parameters: ["phone_number", "country_code"]
  standardize_date:
    description: "Convert date to ISO format"
    parameters: ["date_value", "source_format"]
  calculate_age:
    description: "Calculate age from birth date"
    parameters: ["birth_date", "reference_date"]

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: true
  max_file_size_mb: 100
  backup_count: 5
