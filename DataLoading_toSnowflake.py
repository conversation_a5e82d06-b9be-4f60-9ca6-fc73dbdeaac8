from datetime import date, datetime
import pandas as pd
import pyodbc
import os
from colorama import Fore
from cryptography.fernet import Fernet
import Modules.DataLoadingFunctions as fn
import DataLoadingConfig as config
from snowflake.snowpark import Session

# SQL_Driver = 'ODBC Driver 17 for SQL Server'
# if (config.SQLPort == ''):
#     SQL_Server = config.SQLServer
# else:
#     SQL_Server = config.SQLServer + ',' + config.SQLPort
    
# if (config.Trusted_Connection == 'yes'):
#     SQL_connectString = 'Driver={' + SQL_Driver + '};Server=' + SQL_Server + ';Database=' + config.SQLDatabase + ';Trusted_Connection=yes'
# else:
#     SQLPassword = Fernet(config.SQLpassword_encryptKey).decrypt(config.SQLpassword_encrypted).decode("utf-8")
#     SQL_connectString = 'Driver={' + SQL_Driver + '};Server=' + SQL_Server + ';Database=' + config.SQLDatabase + ';UID=' + config.SQLUserName + ';PWD=' + SQLPassword
   
# try:
#     SQL_DbConnect = pyodbc.connect(SQL_connectString)
#     cursor=SQL_DbConnect.cursor()
# except Exception as ex:
#     print('cannot connect to SQL databse')
#     raise SystemExit()

# #default logFile
# logFile = config.logPath + '\\' + date.today().strftime('%Y%m%d') + '.txt'  
# #default targetTableName
# targetTableName = 'DataLoading_toSQL'
# #latest loadid
# LoadId = fn.getLoadId(SQL_DbConnect)
      
# sourceFileFormat = 'csv'    
# fileList = os.listdir(config.sourcesPath)
# sourceFiles = [x for x in fileList if x.endswith("."+sourceFileFormat)]
# for f in sourceFiles:
#     targetTableName = f.replace("."+sourceFileFormat,"")
#     logFile = config.logPath + '\\' + targetTableName + date.today().strftime('%Y%m%d') + '.txt'
#     fn.writeLog(logFile,'----','-------------------------------------------')
#     fn.writeLog(logFile,'Info','targetTableName: ' + targetTableName)
    
    
#     #create new ETLLoad
#     newLoadQuery = 'INSERT INTO ETLLoad (TableName,Started,StartedLocal,LoadStatus) VALUES(\'' + targetTableName + '\',SYSDATETIME(),SYSDATETIMEOFFSET(),\'In-Progress\')'
#     cursor.execute(newLoadQuery)
#     SQL_DbConnect.commit()
#     LoadId = fn.getLoadId(SQL_DbConnect)
    
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Info','Info','targetTableName: ' + targetTableName)
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Info','Info','logFile: '+logFile)
#     print('logFile: '+logFile)
    
#     fn.writeLog(logFile,'Info','Start loading ' + targetTableName)
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Info','Info','Start loading ' + targetTableName)
    
#     fn.loadData(SQL_DbConnect,LoadId,f,config.logPath)

# #Update ExtractionStartDate.txt for next prophetJobHistory extraction
# try:
#     getNextExtractionStartTime = 'SELECT DATEADD(hour, -' + str(config.nb_hrs) +', MAX(FINISHED)) FROM ETLLoad WHERE LoadStatus=\'Completed\' AND TableName = \'JobHistory\''
#     cursor.execute(getNextExtractionStartTime)
#     nextExtractionStartTime = cursor.fetchone()[0]
#     ExtractionStartTimeFile = config.inputPath+'\\ExtractionStartTime.txt'
#     with open(ExtractionStartTimeFile,'w') as f:
#         f.write(str(nextExtractionStartTime))
#     fn.writeLog(logFile,'Sucess','Updated ExtractionStartDate.txt for next prophetJobHistory extraction')
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Sucess','Sucess','Updated ExtractionStartDate.txt for next prophetJobHistory extraction') 
# except Exception as ex:
#     fn.writeLog(logFile,'Error','Errors occur when updating ExtractionStartTime.txt: ' + str(ex))
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Error','Error','Errors occur when updating ExtractionStartTime.txt: ' + str(ex)) 
    
# #Update RefCostPerHour
# try:
#     CostPerHourFile=config.inputPath+'\\CostPerHour.txt'
#     with open(CostPerHourFile,'r') as f:
#         l = f.readline().strip()
#     key,value = l.split(' = ')
#     costPerHour = value
    
#     cursor.execute('select top 1 CostPerHour from RefCostPerHour order by RefDate desc')
#     lastCostPerHour = cursor.fetchone()[0]
    
#     #print('lastCostPerHour: ' + str(lastCostPerHour))
#     #print('costPerHour: ' + str(costPerHour))
    
#     if str(lastCostPerHour) == str(costPerHour):
#         fn.writeLog(logFile,'Sucess','latest value is ' + str(lastCostPerHour) + ', no update needed')
#         fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Sucess','Sucess','latest value is ' + str(lastCostPerHour) + ', no update needed') 
#     else:
#         updateCostPerHourQuery = 'Insert into RefCostPerHour (CostPerHour, RefDate) values ( ' + costPerHour + ',SYSDATETIME())'
#         cursor.execute(updateCostPerHourQuery)
#         SQL_DbConnect.commit()
#         fn.writeLog(logFile,'Sucess','RefCostPerHour updated')
#         fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Sucess','Sucess','RefCostPerHour updated') 
# except Exception as ex:
#     fn.writeLog(logFile,'Error','Errors occur when updating RefCostPerHour: ' + str(ex))
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Error','Error','Errors occur when updating RefCostPerHour: ' + str(ex)) 
#     raise SystemExit()

# #Update results_space_usage
# try:
#     targetTableName = 'results_space_usage'
#     logFile = config.logPath + '\\' + targetTableName + date.today().strftime('%Y%m%d') + '.txt'
#     #create new ETLLoad for table results_space_usage
#     newLoadQuery = 'INSERT INTO ETLLoad (TableName,Started,StartedLocal,LoadStatus) VALUES(\'' + targetTableName + '\',SYSDATETIME(),SYSDATETIMEOFFSET(),\'In-Progress\')'
#     cursor.execute(newLoadQuery)
#     SQL_DbConnect.commit()
#     LoadId = fn.getLoadId(SQL_DbConnect)
#     cursor.execute('Truncate table results_space_usage')
#     fn.writeLog(logFile,'Info','results_space_usage truncated')
#     fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Info','Info','results_space_usage truncated') 
    
#     cursor.execute('select PEDomain,GUID,JobHistoryId,WorkspaceLink,[Name] as JobName,State,ResultsLink from Workspace_FinishedJobs')
#     rows = cursor.fetchall()
#     for row in rows:
#         resultsPath = row[6]
#         resultsSize = fn.get_folder_size(resultsPath)
#         fn.writeLog(logFile,'Info','Calculating size: ' + resultsPath)
#         fn.writeLogToDB(SQL_DbConnect,LoadId,targetTableName,'Info','Info','Calculating size: ' + resultsPath)
#         if resultsSize is not None:
#             cursor.execute('INSERT INTO results_space_usage(PEDomain,GUID,JobHistoryId,WorkspaceLink,JobName,State,ResultsLink,results_size,loadId,loadedDateTime) VALUES(?,?,?,?,?,?,?,?,?,SYSDATETIME())',(row[0],row[1],row[2],row[3],row[4],row[5],row[6],resultsSize,LoadId))
#             SQL_DbConnect.commit()
    
#     fn.writeLog(logFile,'Sucess','results_space_usage updated')
#     fn.writeLogToDB(SQL_DbConnect,LoadId,'results_space_usage','Sucess','Sucess','results_space_usage updated') 
#     updateLoadCompleted = 'Update ETLLoad set finished = SYSDATETIME(), finishedlocal = SYSDATETIMEOFFSET(), LoadStatus = \'Completed\' where LoadId=' + str(LoadId)
#     cursor.execute(updateLoadCompleted)
#     SQL_DbConnect.commit()
# except Exception as ex:
#     fn.writeLog(logFile,'Error','Errors occur when updating results_space_usage: ' + str(ex))
#     fn.writeLogToDB(SQL_DbConnect,LoadId,'results_space_usage','Error','Error','Errors occur when updating results_space_usage: ' + str(ex))
#     updateLoadFailed = 'Update ETLLoad set finished = SYSDATETIME(), finishedlocal = SYSDATETIMEOFFSET(), LoadStatus = \'Failed\' where LoadId=' + str(LoadId)
#     cursor.execute(updateLoadFailed)
#     raise SystemExit()
    
# cursor.close()
# SQL_DbConnect.close()

def main():
    try:
        # Create Snowpark session
        connection_parameters = {
            "account": config.SnowflakeAccount,
            "user": config.SnowflakeUsername,
            "password": config.SnowflakePassword,
            "role": config.SnowflakeRole,
            "warehouse": config.SnowflakeWarehouse,
            "database": config.SnowflakeDatabase,
            "schema": config.SnowflakeSchema
        }
        
        session = Session.builder.configs(connection_parameters).create()
        
        # Get list of CSV files to process
        source_files = [f for f in os.listdir(config.sourcesPath) if f.endswith('.csv')]
        
        for source_file in source_files:
            # Create new ETL Load record
            load_id = create_etl_load(session, source_file)
            
            # Load and merge data
            rows_processed = fn.load_data_snowflake(session, source_file, load_id)
            print(f"Processed {rows_processed} rows for {source_file}")
            
    except Exception as ex:
        print(f"Error: {str(ex)}")
        raise
    finally:
        if session:
            session.close()

def create_etl_load(session, source_file):
    """Create new ETL Load record and return LoadId"""
    table_name = source_file[:source_file.find('.')]
    insert_query = f"""
    INSERT INTO ETLLoad (TableName, Started, StartedLocal, LoadStatus)
    VALUES ('{table_name}', CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(), 'In-Progress')
    RETURNING LoadId
    """
    result = session.sql(insert_query).collect()
    return result[0]['LOADID']

if __name__ == "__main__":
    main()
