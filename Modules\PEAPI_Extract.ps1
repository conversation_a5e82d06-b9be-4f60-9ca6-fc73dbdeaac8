﻿trap {
    $e = $_.Exception.Message + "
" + $_.InvocationInfo.PositionMessage
    WriteLog $e "RED"
}

# Block system messages
$ErrorActionPreference = "Continue"

# Import PE DLLs to allow access to PE Cmdlets
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\PSPEAPI.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\PEAPI.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\epclib.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\epcplib.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\eplicenselib.dll"

<# PE2022Q1 PE Cmdlets location
Import-Module "C:\Program Files (x86)\ProphetSuite\Prophet Enterprise Studio\PSPEAPI.dll"
Import-Module "C:\Program Files (x86)\ProphetSuite\Prophet Enterprise Studio\PEAPI.dll"
Import-Module "C:\Program Files (x86)\ProphetSuite\Prophet Enterprise Studio\epclib.dll"
Import-Module "C:\Program Files (x86)\ProphetSuite\Prophet Enterprise Studio\epcplib.dll"
Import-Module "C:\Program Files (x86)\ProphetSuite\Prophet Enterprise Studio\eplicenselib.dll"
#>
# Display Prophet Cmdlets
<# Get-Help Prophet #>
#$PEDomain="https://vwmazpe2023q1.fisdev.local/TEST"

Function WriteLog ([string]$LogString,[string]$ForeColor,[string]$LogFile = $Global:LogFile){
    IF ($ForeColor -eq ""){
        Write-Host $Logstring
    } ELSE {
        Write-Host $Logstring -ForegroundColor $ForeColor
    }

    $Time = Get-Date -Format HH:mm:ss
    $LogString = "$Time - $env:UserName - $LogString"
    if ($Logfile){
			$fn=[System.IO.Path]::GetFileName($logfile)
			$mutex = new-object System.Threading.Mutex $false, 'Global\$fn'
			$r=$mutex.WaitOne(1000)
			try{
				$logstring | Out-File $logfile -Append
				$mutex.ReleaseMutex()
			}catch{
				$mutex.ReleaseMutex()
				#Fallback in case Mutex is failing
				if ($Retries -lt 5){
					$Retries=$Retries+1
					WriteLog -LogString $LogString -ForeColor $ForeColor -LogFile $LogFile -ConnectionString $ConnectionString -TextToMask $TextToMask -MaskString $MaskString -Retries $Retries					
				}else{
					$c=1
					$n=$logfile.Substring($logfile.LastIndexOf(".")+1)
					if ($n -match '^\d+$'){
						$c=1+$n
						$logfile = $logfile.Substring(0, $logfile.LastIndexOf(".")+1) + $c
					}else{
						$logfile = $logfile + ".$c"
					}
					if ($c -lt 100){
						$global:LogFile = $LogFile
						$Retries=0
						WriteLog -LogString $LogString -ForeColor $ForeColor -LogFile $LogFile -ConnectionString $ConnectionString -TextToMask $TextToMask -MaskString $MaskString -Retries $Retries
					}
				}
			}
		}
}


# Determine script location for PowerShell
$ScriptDir = Split-Path $script:MyInvocation.MyCommand.Path
Set-Location $ScriptDir

# for multi thread extraction
$maxJobs = 20
$scriptPath = $ScriptDir + '\PEAPI_Extract_multi_thread.ps1'

# Sources Folder
$SrcDir = ($ScriptDir -split 'DataLoading')[0] + 'DataLoading\Sources'
$ExtractDir = $SrcDir + '\extract'

# Log Folder
$logDir = ($ScriptDir -split 'DataLoading')[0] + 'DataLoading\LoadLog'
$Logfile = Get-Date -Format yyyyMMdd
$LogFile = "$logDir\PEAPI_Extract_$Logfile.txt"

# clean up sources and extract folder
WriteLog "Cleaning up Sources folder $SrcDir"
Remove-Item "$SrcDir\*.csv" -Force
WriteLog "Cleaning up extract folder $ExtractDir"
Remove-Item "$ExtractDir\*" -Force

#get Extraction start time
$ExtractionStartTime= Get-Content -Path ..\..\Input\ExtractionStartTime.txt
WriteLog "Extraction start time: $ExtractionStartTime"
$submittedTime = [datetime]::ParseExact($ExtractionStartTime, "yyyy-MM-dd HH:mm:ss.ffffff", $null)

#get PE Domain
$PEDomains= Get-Content -Path ..\..\Input\PEDomain.txt
$ProphetJobHistoryExtracted = $false


foreach ($PEDomain in $PEDomains){
 
WriteLog "Connecting to PE Domain: $PEDomain" 
#$PEDomain_short=$PEDomain.Replace('https://','')
$PEDomain_short=$PEDomain.Split('/')[-1]

##--##-----# Logon and establish a PE session
##--##-----Connect-ProphetSession http://localhost/Prod admin password

# Logon and establish a PE session using Windows Authentication
Connect-ProphetSessionWinAuthent $PEDomain

# Get ProphetJobHistory
if($ProphetJobHistoryExtracted -eq $false){
    # ProphetJobHistory -> jobhistory is extracted from Matrix database which is not depended on PEDomain
    WriteLog "Extracting ProphetJobHistory: $ExtractDir\ProphetJobHistory.csv"
    $DownloadFilePath = "$ExtractDir\ProphetJobHistory.csv"
    #$submittedTime = (Get-Date -year 2024 -month 1 -day 1)
    try {
        $ProphetJobHistory = Get-ProphetJobHistory -DownloadFilePath $DownloadFilePath -SubmittedStart $submittedTime
    
        #Split ProphetJobHistory.csv into JobHistory.csv and MachineJobHistory.csv
        $ProphetJobHistory = Get-Content -Path $ExtractDir\ProphetJobHistory.csv
        $searchString = "MachineJobHistoryId,JobHistoryId,JobId,StartTime,EndTime,RunStage,MachineName,NoOfCores,Completed,MachineLicensedCoreType"
        $splitLine = ($ProphetJobHistory| Select-String -Pattern $searchString).LineNumber
        $JobHistory = $ProphetJobHistory[0..($splitLine - 2)]
        $MachineJobHistory = $ProphetJobHistory[($splitLine-1)..($ProphetJobHistory.Count - 1)]
        $JobHistory | Set-Content -Path $SrcDir\JobHistory.csv
        $MachineJobHistory | Set-Content -Path $SrcDir\MachineJobHistory.csv
    } catch { 
        WriteLog "Error extracting ProphetJobHistory: $_" "RED"
    }
    $ProphetJobHistoryExtracted = $true
}


# Get list of workspaces
WriteLog "Extracting workspaces: $ExtractDir\Workspaces_${PEDomain_short}.csv"
$workspaces = Get-ProphetWorkspaces

# Get workspace names for multi-thread extract
$wksNames = $workspaces.link.name


$workspaces.Data | Select-Object CompartmentChangeInProgress,ProductDefinitionsFilledIn,VariableGroupsLoaded,FolderName,OriginalImportLocation,VersionNumber,BackupVersion,CompartmentLink,CompartmentFolderName,AuditComment,ClientSideDataOnly,EnhancedCumulativeNormal,UploadedOn,NewUnsavedEntity,FullData,EntityType,GUID,Name,LastModifiedOn,LastModifiedBy,CreatedOn,CreatedBy,AvailableItems,BuiltIn,StoredInFile,DataOutOfDate,StoredName,LockingInfo,IsEditable,IsLocked | Export-CSV "$ExtractDir\Workspaces_${PEDomain_short}.csv" -NoTypeInformation

#Add PEDomain to workspaces
$Datafile = Import-Csv "$ExtractDir\Workspaces_${PEDomain_short}.csv"
$Datafile | ForEach-Object{
    $_ | Add-Member -MemberType NoteProperty -Name PEDomain -Value "$PEDomain_short"
    $_
} | Export-Csv "$ExtractDir\Workspaces_${PEDomain_short}.csv" -NoTypeInformation



# FinishedJobs
WriteLog "Extracting FinishedJobs: $ExtractDir\Workspace_FinishedJobs_${PEDomain_short}.csv"
WriteLog "Extracting FinishedJobsCategoryValues: $ExtractDir\Workspace_FinishedJobsCategoryValues_${PEDomain_short}.csv"

#$csvFile = "$ExtractDir\Workspace_FinishedJobs_${PEDomain_short}.csv"
#foreach ($workspace in $workspaces){
#    $allWksFinishedJobs = $workspace.GetFinishedJobs()
#    $data = $allWksFinishedJobs.Data | select-object JobLink,RunlogLink,ManagedTablesLink,PDFRunlogLink,ResultsLink,WorkspaceLink,CompartmentLink,SystemLogsLink,State,StateDescription,Submitted,PrepStarted,PrepFinished,RunStarted,RunFinished,PrepTime,RunTime,PrepAndRunTime,ErrorCount,WarningCount,SuccessfulSPs,SuccessfulSims,SuccessfulProdsAccums,Detail,SubmittedByLink,WorkspaceVersionNr,RunSettingVersionNr,RunStructureVersionNr,JobTemplateVersionNr,CreateDownloadableResultsFile,AutoDownloadResults,PEJobType,EnableAVX,EnableGPU,PrepBaseCoreHours,RunBaseCoreHours,PrepBurstCoreHours,RunBurstCoreHours,TotalBurstCoreHours,LicensedCoreType,JobHistoryId,SpecificLogInfo,ProduceJobSpecificLogInfo,ApplyJobSpecificLogInfoToNestedStructures,OverwriteDataStorageSetup,DataStorageSetup,OverriddenFileSystemOutputPath,OverriddenRepository,DataStorageOverrideAllowedInDomain,ResultsMovedLocation,CEVersion,UseDefaultCEVersion,NewUnsavedEntity,FullData,EntityType,GUID,Name,LastModifiedOn,LastModifiedBy,CreatedOn,CreatedBy,AvailableItems,BuiltIn,StoredInFile,DataOutOfDate,StoredName,LockingInfo,IsEditable,IsLocked 
#
#    if (Test-Path $csvFile) {
#        $data | export-csv $csvFile -NoTypeInformation -Append
#    } else{
#        $data | export-csv $csvFile -NoTypeInformation
#    }
#}

# extract finished jobs with multi thread jobs
$jobs = @()

foreach ($wksName in $wksNames) {
    while ($jobs.Count -ge $maxJobs) {
        $jobs = $jobs | Where-Object { $_.State -eq 'Running' }
        Start-Sleep -Seconds 1
    }
    $jobs += Start-Job -FilePath $scriptPath -ArgumentList $wksName, $ExtractDir, "GetFinishedJobs", $PEDomain, $PEDomain_short, $submittedTime

}

$jobs | ForEach-Object { $_ | Wait-Job | Receive-Job }



#Add PEDomain to FinishedJobs
$Datafile = Import-Csv "$ExtractDir\Workspace_FinishedJobs_${PEDomain_short}.csv"
$Datafile | ForEach-Object{
    $_ | Add-Member -MemberType NoteProperty -Name PEDomain -Value "$PEDomain_short"
    $_
} | Export-Csv "$ExtractDir\Workspace_FinishedJobs_${PEDomain_short}.csv" -NoTypeInformation

#Add PEDomain to _FinishedJobsCategoryValues
$DatafilePath="$ExtractDir\Workspace_FinishedJobsCategoryValues_${PEDomain_short}.csv"
 
if(Test-Path $DatafilePath){
    $Datafile = Import-Csv "$ExtractDir\Workspace_FinishedJobsCategoryValues_${PEDomain_short}.csv"
 
    $Datafile | ForEach-Object{
        $_ | Add-Member -MemberType NoteProperty -Name PEDomain -Value "$PEDomain_short"
        $_
    } | Export-Csv "$ExtractDir\Workspace_FinishedJobsCategoryValues_${PEDomain_short}.csv" -NoTypeInformation
}


<# Jobs 
WriteLog "Extracting Jobs: $ExtractDir\Workspace_Jobs_${PEDomain_short}.csv"

$csvFile = "$ExtractDir\Workspace_Jobs_${PEDomain_short}.csv"
foreach ($workspace in $workspaces){
    $allWksJobs = $workspace.GetJobs()
    $data = $allWksJobs.Data | Select-Object GUID,Name,JobFolderName,JobRun,JobTemplateVersionNr,JobTemplateLink,WorkspaceLink,CompartmentLink,RunSettingLink,RunStructureLink,Priority,SplitType,InnerSplitType,SplitByModelPoint,ModelPointBatchSize,MinModelPointBatchSize,MaxModelPointBatchSize,OptimiseWithMPFileContents,OptimiseWithTableContents,TableAndMPOptimisations,RetainBinaryModelPointFiles,RunScope,ForceRegeneration,EndOfJobProcess,StartFromLevelNumber,EnableMultiThreading,DisableMultiThreading,SetMinimumWorkerMemory,MinimumWorkerMemory,MinNoResources,MaxNoResources,MinNoCoresPrepare,MinNoResourcesPrepare,MaxNoCoresPrepare,MaxNoResourcesPrepare,SimsPerTask,JobRetries,RunlogGoalSeekingMessages,RunlogSystemMessages,RunlogUserSpecifiedMessages,RunlogExtendedFormulaMessages,NotifySubmittingUser,CreateDownloadableResultsFile,AutoDownloadResults,HeadNodeInDependentChain,OperatingSystemType,CSVAuditLog,PDFRunLog,ForceRuntimeByMachines,RunLocal,IntexVersion,CPUArchitecture,Compiler,RequireValidatedDatasets,RequireApprovedInputAssumptions,EnableAVX,EnableGPU,LicensedCoreType,ProduceJobSpecificLogInfo,ApplyJobSpecificLogInfoToNestedStructures,SelectedCalculationEngine,UseDefaultCalculationEngine,OverwriteDataStorageSetup,DataStorageSetup,OverriddenRepository,OverriddenFileSystemOutputPath,DataStorageOverrideAllowedInDomain,IsWorkingCopyAndNestedJob,NewUnsavedEntity,FullData,EntityType,LastModifiedOn,LastModifiedBy,CreatedOn,CreatedBy,BuiltIn,StoredInFile,DataOutOfDate,StoredName,LockingInfo,IsEditable,IsLocked

    if (Test-Path $csvFile) {
        $data | export-csv $csvFile -NoTypeInformation -Append
    } else{
        $data | export-csv $csvFile -NoTypeInformation
    }
}

#Add PEDomain to Jobs
$Datafile = Import-Csv "$ExtractDir\Workspace_jobs_${PEDomain_short}.csv"
$Datafile | ForEach-Object{
    $_ | Add-Member -MemberType NoteProperty -Name PEDomain -Value "$PEDomain_short"
    $_
} | Export-Csv "$ExtractDir\Workspace_jobs_${PEDomain_short}.csv" -NoTypeInformation
#>


# JobTemplates
WriteLog "Extracting JobTemplates: $ExtractDir\Workspace_JobTemplates_${PEDomain_short}.csv"
WriteLog "Extracting JobTemplatesCategoryValues: $ExtractDir\Workspace_JobTemplatesCategoryValues_${PEDomain_short}.csv"


#$csvFile = "$ExtractDir\Workspace_JobTemplates_${PEDomain_short}.csv"
#foreach ($workspace in $workspaces){
#    $allWksJobTemplates = $workspace.GetJobTemplates()
#    $data = $allWksJobTemplates.Data | Select-Object GUID,Name,WorkspaceLink,CompartmentLink,RunSettingLink,RunStructureLink,MachineGroupLink,MachineGroupPrepareLink,Priority,SplitType,InnerSplitType,SplitByModelPoint,ModelPointBatchSize,MinModelPointBatchSize,MaxModelPointBatchSize,OptimiseWithMPFileContents,OptimiseWithTableContents,TableAndMPOptimisations,RetainBinaryModelPointFiles,RunScope,ForceRegeneration,EndOfJobProcess,StartFromLevelNumber,EnableMultiThreading,DisableMultiThreading,SetMinimumWorkerMemory,MinimumWorkerMemory,MinNoResources,MaxNoResources,MinNoCoresPrepare,MinNoResourcesPrepare,MaxNoCoresPrepare,MaxNoResourcesPrepare,SimsPerTask,JobRetries,RunlogGoalSeekingMessages,RunlogSystemMessages,RunlogUserSpecifiedMessages,RunlogExtendedFormulaMessages,NotifySubmittingUser,CopyResultsDir,CreateDownloadableResultsFile,AutoDownloadResults,HeadNodeInDependentChain,OperatingSystemType,CSVAuditLog,PDFRunLog,ForceRuntimeByMachines,RunLocal,IntexVersion,CPUArchitecture,Compiler,RequireValidatedDatasets,RequireApprovedInputAssumptions,EnableAVX,EnableGPU,LicensedCoreType,ProduceJobSpecificLogInfo,ApplyJobSpecificLogInfoToNestedStructures,SelectedCalculationEngine,UseDefaultCalculationEngine,OverwriteDataStorageSetup,DataStorageSetup,OverriddenRepository,OverriddenFileSystemOutputPath,DataStorageOverrideAllowedInDomain,IsWorkingCopyAndNestedJob,NewUnsavedEntity,FullData,EntityType,LastModifiedOn,LastModifiedBy,CreatedOn,CreatedBy,BuiltIn,StoredInFile,DataOutOfDate,StoredName,LockingInfo,IsEditable,IsLocked 
#
#    if (Test-Path $csvFile) {
#        $data | export-csv $csvFile -NoTypeInformation -Append
#    } else{
#        $data | export-csv $csvFile -NoTypeInformation
#    }
#}


# extract JobTemplates with multi thread jobs
$jobs = @()

foreach ($wksName in $wksNames) {
    while ($jobs.Count -ge $maxJobs) {
        $jobs = $jobs | Where-Object { $_.State -eq 'Running' }
        Start-Sleep -Seconds 1
    }
    $jobs += Start-Job -FilePath $scriptPath -ArgumentList $wksName, $ExtractDir, "GetJobTemplates", $PEDomain, $PEDomain_short, $submittedTime

}

$jobs | ForEach-Object { $_ | Wait-Job | Receive-Job }


#Add PEDomain to JobTemplates
$Datafile = Import-Csv "$ExtractDir\Workspace_JobTemplates_${PEDomain_short}.csv"
$Datafile | ForEach-Object{
    $_ | Add-Member -MemberType NoteProperty -Name PEDomain -Value "$PEDomain_short"
    $_
} | Export-Csv "$ExtractDir\Workspace_JobTemplates_${PEDomain_short}.csv" -NoTypeInformation


Disconnect-ProphetSession 
WriteLog "Disconnected from PE Domain: $PEDomain" 


#Add PEDomain to JobTemplatesCategoryValues
$DatafilePath="$ExtractDir\Workspace_JobTemplatesCategoryValues_${PEDomain_short}.csv"
if(Test-Path $DatafilePath){
 
    $Datafile = Import-Csv "$ExtractDir\Workspace_JobTemplatesCategoryValues_${PEDomain_short}.csv"
 
    $Datafile | ForEach-Object{
        $_ | Add-Member -MemberType NoteProperty -Name PEDomain -Value "$PEDomain_short"
        $_
    } | Export-Csv "$ExtractDir\Workspace_JobTemplatesCategoryValues_${PEDomain_short}.csv" -NoTypeInformation
}
}

#Merge workspace files
WriteLog "Merging Workspaces files"
$Workspaces_files = Get-ChildItem -Path $ExtractDir -Filter "Workspaces*.csv"
$firstfile = $true
foreach ($Workspaces_file in $Workspaces_files){
    $Workspaces = Get-Content -Path $Workspaces_file.FullName
    if($firstfile){
        $Workspaces | Set-Content -Path $SrcDir\Workspaces.csv
        $firstFile = $false
    } else {
        $Workspaces | Select-Object -Skip 1 | Add-Content -Path $SrcDir\Workspaces.csv
    }
}

<#Merge JobHistory files
WriteLog "Merging JobHistory files"
$JobHistory_files = Get-ChildItem -Path $ExtractDir -Filter "JobHistory*.csv"
$firstfile = $true
foreach ($JobHistory_file in $JobHistory_files){
    $JobHistory = Get-Content -Path $JobHistory_file.FullName
    if($firstfile){
        $JobHistory | Set-Content -Path $SrcDir\JobHistory.csv
        $firstFile = $false
    } else {
        $JobHistory | Select-Object -Skip 1 | Add-Content -Path $SrcDir\JobHistory.csv
    }
}

#Merge MachineJobHistory files
WriteLog "Merging MachineJobHistory files"
$MachineJobHistory_files = Get-ChildItem -Path $ExtractDir -Filter "MachineJobHistory*.csv"
$firstfile = $true
foreach ($MachineJobHistory_file in $MachineJobHistory_files){
    $MachineJobHistory = Get-Content -Path $MachineJobHistory_file.FullName
    if($firstfile){
        $MachineJobHistory | Set-Content -Path $SrcDir\MachineJobHistory.csv
        $firstFile = $false
    } else {
        $MachineJobHistory | Select-Object -Skip 1 | Add-Content -Path $SrcDir\MachineJobHistory.csv
    }
}
#>

<#Merge Workspace_Jobs files
WriteLog "Merging Workspace_Jobs files"
$Workspace_Jobs_files = Get-ChildItem -Path $ExtractDir -Filter "Workspace_Jobs*.csv"
$firstfile = $true
foreach ($Workspace_Jobs_file in $Workspace_Jobs_files){
    $Workspace_Jobs = Get-Content -Path $Workspace_Jobs_file.FullName
    if($firstfile){
        $Workspace_Jobs | Set-Content -Path $SrcDir\Workspace_Jobs.csv
        $firstFile = $false
    } else {
        $Workspace_Jobs | Select-Object -Skip 1 | Add-Content -Path $SrcDir\Workspace_Jobs.csv
    }
}
#>

#Merge Workspace_FinishedJobs files
WriteLog "Merging Workspace_FinishedJobs files"
$Workspace_FinishedJobs_files = Get-ChildItem -Path $ExtractDir -Filter "Workspace_FinishedJobs_*.csv"
$firstfile = $true
foreach ($Workspace_FinishedJobs_file in $Workspace_FinishedJobs_files){
    $Workspace_FinishedJobs = Get-Content -Path $Workspace_FinishedJobs_file.FullName
    if($firstfile){
        $Workspace_FinishedJobs | Set-Content -Path $SrcDir\Workspace_FinishedJobs.csv
        $firstFile = $false
    } else {
        $Workspace_FinishedJobs | Select-Object -Skip 1 | Add-Content -Path $SrcDir\Workspace_FinishedJobs.csv
    }
}

#Merge Workspace_JobTemplates files
WriteLog "Merging Workspace_JobTemplates files"
$Workspace_JobTemplates_files = Get-ChildItem -Path $ExtractDir -Filter "Workspace_JobTemplates_*.csv"
$firstfile = $true
foreach ($Workspace_JobTemplates_file in $Workspace_JobTemplates_files){
    $Workspace_JobTemplates = Get-Content -Path $Workspace_JobTemplates_file.FullName
    if($firstfile){
        $Workspace_JobTemplates | Set-Content -Path $SrcDir\Workspace_JobTemplates.csv
        $firstFile = $false
    } else {
        $Workspace_JobTemplates | Select-Object -Skip 1 | Add-Content -Path $SrcDir\Workspace_JobTemplates.csv
    }
}

#Merge Workspace_FinishedJobsCategoryValues files
WriteLog "Merging Workspace_FinishedJobsCategoryValues files"
$Workspace_FinishedJobs_Category_files = Get-ChildItem -Path $ExtractDir -Filter "Workspace_FinishedJobsCategoryValues_*.csv"
$firstfile = $true
foreach ($Workspace_FinishedJobs_Category_file in $Workspace_FinishedJobs_Category_files){
    $Workspace_FinishedJobs_Category = Get-Content -Path $Workspace_FinishedJobs_Category_file.FullName
    if($firstfile){
        $Workspace_FinishedJobs_Category | Set-Content -Path $SrcDir\Workspace_FinishedJobsCategoryValues.csv
        $firstFile = $false
    } else {
        $Workspace_FinishedJobs_Category | Select-Object -Skip 1 | Add-Content -Path $SrcDir\Workspace_FinishedJobsCategoryValues.csv
    }
}
 
#Merge Workspace_JobTemplatesCategoryValues files
WriteLog "Merging Workspace_JobTemplatesCategoryValues files"
$Workspace_JobTemplates_Category_files = Get-ChildItem -Path $ExtractDir -Filter "Workspace_JobTemplatesCategoryValues_*.csv"
$firstfile = $true
foreach ($Workspace_JobTemplates_Category_file in $Workspace_JobTemplates_Category_files){
    $Workspace_JobTemplates_Category = Get-Content -Path $Workspace_JobTemplates_Category_file.FullName
    if($firstfile){
        $Workspace_JobTemplates_Category | Set-Content -Path $SrcDir\Workspace_JobTemplatesCategoryValues.csv
        $firstFile = $false
    } else {
        $Workspace_JobTemplates_Category | Select-Object -Skip 1 | Add-Content -Path $SrcDir\Workspace_JobTemplatesCategoryValues.csv
    }
}
