﻿param (
    [Parameter(Mandatory=$true)]
    [string]$wksName,

    [Parameter(Mandatory=$true)]
    [string]$ExtractDir,

    [Parameter(Mandatory=$true)]
    [string]$getmethod,

    [Parameter(Mandatory=$true)]
    [string]$PEDomain,

    [Parameter(Mandatory=$true)]
    [string]$PEDomain_short,

    [Parameter(Mandatory=$true)]
    [datetime]$ExtractStartTime
)

Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\PSPEAPI.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\PEAPI.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\epclib.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\epcplib.dll"
Import-Module "C:\Program Files\ProphetSuite\Prophet Enterprise Studio\eplicenselib.dll"

$success = $false
$attempt = 0
$retryCount = 20

while (-not $success -and $attempt -lt $retryCount)
{
    try {
        Connect-ProphetSessionWinAuthent $PEDomain
        $success = $true
    } catch {
        Write-Warning "Failed to connect to $PEDomain. Attempt $($attempt + 1) of $retryCount. Retrying in 5 seconds..."
        Start-Sleep -Seconds 5
        $attempt++
    }
}


$workspace = Get-ProphetWorkspace($wksName)

if ($getmethod -eq "GetFinishedJobs") {
    $csvFile = "$ExtractDir\Workspace_FinishedJobs_${PEDomain_short}.csv"
    $allWksFinishedJobs = $workspace.GetFinishedJobs()
    $data = $allWksFinishedJobs.Data |Where-Object {$_.Submitted -ge $ExtractStartTime }| Select-Object JobLink, RunlogLink, ManagedTablesLink, PDFRunlogLink, ResultsLink, WorkspaceLink, CompartmentLink, SystemLogsLink, State, StateDescription, Submitted, PrepStarted, PrepFinished, RunStarted, RunFinished, PrepTime, RunTime, PrepAndRunTime, ErrorCount, WarningCount, SuccessfulSPs, SuccessfulSims, SuccessfulProdsAccums, Detail, SubmittedByLink, WorkspaceVersionNr, RunSettingVersionNr, RunStructureVersionNr, JobTemplateVersionNr, CreateDownloadableResultsFile, AutoDownloadResults, PEJobType, EnableAVX, EnableGPU, PrepBaseCoreHours, RunBaseCoreHours, PrepBurstCoreHours, RunBurstCoreHours, TotalBurstCoreHours, LicensedCoreType, JobHistoryId, SpecificLogInfo, ProduceJobSpecificLogInfo, ApplyJobSpecificLogInfoToNestedStructures, OverwriteDataStorageSetup, DataStorageSetup, OverriddenFileSystemOutputPath, OverriddenRepository, DataStorageOverrideAllowedInDomain, ResultsMovedLocation, CEVersion, UseDefaultCEVersion, NewUnsavedEntity, FullData, EntityType, GUID, Name, LastModifiedOn, LastModifiedBy, CreatedOn, CreatedBy, AvailableItems, BuiltIn, StoredInFile, DataOutOfDate, StoredName, LockingInfo, IsEditable, IsLocked
}

if ($getmethod -eq "GetJobTemplates") {
    $csvFile = "$ExtractDir\Workspace_JobTemplates_${PEDomain_short}.csv"
    $allWksJobTemplates = $workspace.GetJobTemplates()
    $data = $allWksJobTemplates.Data |Where-Object {$_.LastModifiedOn -ge $ExtractStartTime }| Select-Object GUID,Name,WorkspaceLink,CompartmentLink,RunSettingLink,RunStructureLink,MachineGroupLink,MachineGroupPrepareLink,Priority,SplitType,InnerSplitType,SplitByModelPoint,ModelPointBatchSize,MinModelPointBatchSize,MaxModelPointBatchSize,OptimiseWithMPFileContents,OptimiseWithTableContents,TableAndMPOptimisations,RetainBinaryModelPointFiles,RunScope,ForceRegeneration,EndOfJobProcess,StartFromLevelNumber,EnableMultiThreading,DisableMultiThreading,SetMinimumWorkerMemory,MinimumWorkerMemory,MinNoResources,MaxNoResources,MinNoCoresPrepare,MinNoResourcesPrepare,MaxNoCoresPrepare,MaxNoResourcesPrepare,SimsPerTask,JobRetries,RunlogGoalSeekingMessages,RunlogSystemMessages,RunlogUserSpecifiedMessages,RunlogExtendedFormulaMessages,NotifySubmittingUser,CopyResultsDir,CreateDownloadableResultsFile,AutoDownloadResults,HeadNodeInDependentChain,OperatingSystemType,CSVAuditLog,PDFRunLog,ForceRuntimeByMachines,RunLocal,IntexVersion,CPUArchitecture,Compiler,RequireValidatedDatasets,RequireApprovedInputAssumptions,EnableAVX,EnableGPU,LicensedCoreType,ProduceJobSpecificLogInfo,ApplyJobSpecificLogInfoToNestedStructures,SelectedCalculationEngine,UseDefaultCalculationEngine,OverwriteDataStorageSetup,DataStorageSetup,OverriddenRepository,OverriddenFileSystemOutputPath,DataStorageOverrideAllowedInDomain,IsWorkingCopyAndNestedJob,NewUnsavedEntity,FullData,EntityType,LastModifiedOn,LastModifiedBy,CreatedOn,CreatedBy,BuiltIn,StoredInFile,DataOutOfDate,StoredName,LockingInfo,IsEditable,IsLocked 
}

$success = $false
$attempt = 0
$retryCount = 20

if (Test-Path $csvFile) {
    while (-not $success -and $attempt -lt $retryCount)
    {
        try {
            $data | Export-Csv -Path $csvFile -NoTypeInformation -Append
            $success = $true
        } catch {
            #Write-Warning "Failed to write to $csvFile. Attempt $($attempt + 1) of $retryCount. Retrying in 5 seconds..."
            Start-Sleep -Seconds 5
            $attempt++
        }
    }
} else {
    $data | Export-Csv $csvFile -NoTypeInformation
}

Disconnect-ProphetSession 








