# BI Dashboard Data Loading System - User Guide

## Table of Contents
1. [System Overview](#system-overview)
2. [Prerequisites](#prerequisites)
3. [System Architecture](#system-architecture)
4. [Configuration](#configuration)
5. [Data Extraction Process](#data-extraction-process)
6. [Data Loading Process](#data-loading-process)
7. [Running the System](#running-the-system)
8. [Monitoring and Logging](#monitoring-and-logging)
9. [Troubleshooting](#troubleshooting)
10. [Data Sources and Tables](#data-sources-and-tables)

## System Overview

The BI Dashboard Data Loading System is an automated ETL (Extract, Transform, Load) solution designed to extract data from Prophet Enterprise (PE) systems via API and load it into SQL Server and Snowflake databases for business intelligence reporting.

### Key Features
- **Automated Data Extraction**: Extracts data from multiple PE domains using PowerShell API scripts
- **Dual Database Support**: Loads data into both SQL Server and Snowflake
- **Incremental Loading**: Supports incremental data extraction based on timestamps
- **Error Handling**: Comprehensive logging and error handling mechanisms
- **Multi-threaded Processing**: Supports parallel extraction for improved performance
- **Data Validation**: Merge operations with upsert functionality

## Prerequisites

### Software Requirements
- **Python 3.x** with required modules:
  - pandas
  - pyodbc
  - colorama
  - cryptography
  - snowflake-snowpark-python
- **PowerShell** with Prophet Enterprise API modules
- **SQL Server ODBC Driver 17**
- **Prophet Enterprise Studio** with API access

### System Access
- Windows Authentication or SQL Server credentials for database access
- Prophet Enterprise domain access with appropriate permissions
- Snowflake account with proper role assignments

## System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PE Domains    │───▶│  Data Extraction │───▶│   CSV Files     │
│  (API Sources)  │    │   (PowerShell)   │    │   (Sources/)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   SQL Server    │◀───│  Data Loading    │◀───│  Data Processing│
│   Database      │    │   (Python)       │    │   & Transform   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Snowflake     │
                       │   Database      │
                       └─────────────────┘
```

## Configuration

### 1. Database Configuration (DataLoadingConfig.py)

#### SQL Server Settings
```python
SQLServer = r'vwmazpssql\SQL17'
SQLDatabase = 'ETLConfiguration_PEAPI'
Trusted_Connection = 'yes'  # Use Windows Authentication
```

#### Snowflake Settings
```python
SnowflakeAccount = 'FISDEV.us-east-1'
SnowflakeWarehouse = 'CAPITAL_MARKETS_IDR__WH'
SnowflakeDatabase = 'ETLCONFIGURATION_PEAPI'
SnowflakeSchema = 'LANDING'
SnowflakeRole = 'SERV_RL__SERVICE_IDR_REPORTING'
SnowflakeUsername = 'SERVICE_IDR_REPORTING'
```

#### File Paths
```python
wrk_path = r'C:\Users\<USER>\Documents\Internal\Standard\DataLoading'
sourcesPath = wrk_path + '\\Sources'
logPath = wrk_path + '\\Loadlog'
inputPath = wrk_path + '\\Input'
```

### 2. PE Domain Configuration
Edit `Input/PEDomain.txt` to specify Prophet Enterprise domains:
```
https://vwmazpe2023q1.fisdev.local/TEST
https://vwmazpe2023q1.fisdev.local/DEMO
```

### 3. Extraction Time Configuration
The system uses `Input/ExtractionStartTime.txt` to track incremental extractions.

## Data Extraction Process

### Extracted Data Types
The system extracts the following data from PE domains:

1. **Job History** - Completed job execution details
2. **Machine Job History** - Machine-specific job execution data
3. **Workspaces** - Workspace configuration and metadata
4. **Workspace Finished Jobs** - Job completion details by workspace
5. **Job Templates** - Available job templates and configurations
6. **Category Values** - Job categorization and classification data

### Extraction Workflow
1. **Initialize**: Connect to each PE domain using Windows Authentication
2. **Extract Data**: Query PE API for each data type since last extraction time
3. **Export CSV**: Save extracted data as CSV files in Sources folder
4. **Multi-threading**: Process multiple domains in parallel (max 20 concurrent jobs)
5. **Cleanup**: Remove old CSV files before new extraction

## Data Loading Process

### SQL Server Loading
- **Method**: Merge operations (UPSERT)
- **Primary Keys**: Uses table primary keys or first column as key
- **Tracking**: Each load gets unique LoadId for audit trail
- **Status**: Updates ETLLoad table with completion status

### Snowflake Loading
- **Method**: Snowpark DataFrame operations with merge
- **Schema**: Automatic schema inference from CSV headers
- **Columns**: Adds LoadId and LoadedDateTime for tracking
- **Error Handling**: Comprehensive error logging and status updates

## Running the System

### Option 1: Complete Automated Run
```cmd
Run.cmd
```
This executes the full pipeline:
1. PE API data extraction
2. SQL Server data loading
3. IDR Governance data updates

### Option 2: Step-by-Step Execution

#### Step 1: Install Dependencies
```cmd
00_install_python_modules.cmd
```

#### Step 2: Extract Data
```cmd
01_PEAPI_Extract.cmd
```

#### Step 3: Load to SQL Server
```cmd
02_DataLoading_toSQL.cmd
```

#### Step 4: Load to Snowflake
```python
python DataLoading_toSnowflake.py
```

### Option 3: Manual Python Execution
```python
# For SQL Server
python DataLoading_toSQL.py

# For Snowflake
python DataLoading_toSnowflake.py
```

## Monitoring and Logging

### Log Files Location
- **Path**: `LoadLog/` directory
- **Format**: `[TableName][YYYYMMDD].txt`
- **Content**: Timestamped entries with operation details

### Log Types
- **Info**: General processing information
- **Success**: Successful operations
- **Error**: Error conditions and exceptions

### Database Logging
- **ETLLoad Table**: Tracks each load operation with status
- **LoadLog Table**: Detailed operation logs stored in database

### Sample Log Entry
```
14:30:25 - username - Info     Processing JobHistory.csv
14:30:26 - username - Success  Loaded 150 rows to JobHistory table
```

## Troubleshooting

### Common Issues

#### 1. Connection Errors
**Problem**: Cannot connect to SQL Server/Snowflake
**Solution**: 
- Verify connection strings in DataLoadingConfig.py
- Check network connectivity
- Validate credentials and permissions

#### 2. PE API Access Issues
**Problem**: PowerShell script fails to connect to PE domain
**Solution**:
- Verify PE domain URLs in PEDomain.txt
- Check Windows Authentication credentials
- Ensure Prophet Enterprise modules are installed

#### 3. File Permission Errors
**Problem**: Cannot write to Sources or LoadLog directories
**Solution**:
- Check folder permissions
- Verify paths in DataLoadingConfig.py
- Run as administrator if necessary

#### 4. Data Loading Failures
**Problem**: CSV files not loading to database
**Solution**:
- Check CSV file format and headers
- Verify target table schema matches source data
- Review error logs for specific issues

### Error Recovery
- Failed loads are marked in ETLLoad table with 'Failed' status
- Review LoadLog table for detailed error information
- Re-run specific components after resolving issues

## Data Sources and Tables

### Source Tables Created
1. **JobHistory** - Job execution history and performance metrics
2. **MachineJobHistory** - Machine-specific job execution data
3. **Workspaces** - Workspace configuration and metadata
4. **Workspace_FinishedJobs** - Completed jobs by workspace
5. **Workspace_JobTemplates** - Available job templates
6. **Workspace_FinishedJobsCategoryValues** - Job categorization data
7. **results_space_usage** - Storage utilization metrics

### Key Fields
- **LoadId**: Unique identifier for each ETL operation
- **LoadedDateTime**: Timestamp of data loading
- **PEDomain**: Source Prophet Enterprise domain
- **JobHistoryId**: Unique job execution identifier
- **Workspace**: Prophet workspace name
- **State**: Job execution status (Completed, Failed, etc.)

### Business Intelligence Views
The system creates several views for BI reporting:
- **vwBIPEDomain_Workspace**: Domain and workspace relationships
- **vwBIPECategories**: Job categorization summary
- **vwBIPEJobCores_PEAPI**: Core job execution metrics

## Advanced Configuration

### Password Encryption
For enhanced security, use the password encryption utility:

```cmd
EncryptPassword.cmd
```

This will prompt for a password and generate encrypted values for:
- `SQLpassword_encrypted`
- `SQLpassword_encryptKey`
- `SnowflakePassword_encrypted`
- `SnowflakePassword_encryptKey`

### Multi-threading Configuration
Adjust the maximum number of concurrent extraction jobs in `PEAPI_Extract.ps1`:
```powershell
$maxJobs = 20  # Modify based on system capacity
```

### Extraction Time Window
Configure the lookback period for incremental extractions in `DataLoadingConfig.py`:
```python
nb_hrs = 2  # Hours before last successful load
```

## Data Quality and Validation

### Data Validation Checks
The system performs several validation checks:

1. **Schema Validation**: Ensures CSV headers match target table columns
2. **Data Type Validation**: Validates data types during loading
3. **Duplicate Detection**: Uses primary keys to prevent duplicate records
4. **Referential Integrity**: Maintains relationships between related tables

### Data Refresh Strategy
- **Incremental Loading**: Only processes new/changed data since last extraction
- **Full Refresh**: Can be triggered by clearing ExtractionStartTime.txt
- **Merge Operations**: Updates existing records and inserts new ones

## Performance Optimization

### Best Practices
1. **Scheduled Execution**: Run during off-peak hours for optimal performance
2. **Resource Monitoring**: Monitor CPU and memory usage during extraction
3. **Network Optimization**: Ensure stable network connection to PE domains
4. **Database Maintenance**: Regular index maintenance on target tables

### Performance Metrics
Monitor these key metrics:
- **Extraction Time**: Time to extract data from PE domains
- **Loading Time**: Time to load data into databases
- **Row Counts**: Number of records processed per table
- **Error Rates**: Percentage of failed operations

## Security Considerations

### Access Control
- **Database Access**: Use least-privilege principle for database connections
- **File System**: Secure access to Sources and LoadLog directories
- **PE API Access**: Limit API access to necessary domains only

### Data Protection
- **Encryption**: Use encrypted passwords for database connections
- **Audit Trail**: Maintain complete audit logs of all operations
- **Data Retention**: Implement appropriate data retention policies

## Maintenance and Administration

### Regular Maintenance Tasks

#### Daily
- Monitor log files for errors
- Verify successful completion of scheduled runs
- Check disk space in Sources and LoadLog directories

#### Weekly
- Review performance metrics and trends
- Clean up old log files (older than 30 days)
- Validate data quality and completeness

#### Monthly
- Update PE domain configurations if needed
- Review and optimize database performance
- Test disaster recovery procedures

### Backup and Recovery
- **Database Backups**: Ensure regular backups of target databases
- **Configuration Backups**: Backup configuration files and scripts
- **Recovery Testing**: Regularly test recovery procedures

## Integration with BI Tools

### Connecting BI Tools
The loaded data can be accessed by various BI tools:

#### Power BI
```
Server: vwmazpssql\SQL17
Database: ETLConfiguration_PEAPI
Authentication: Windows Authentication
```

#### Tableau
Connect using SQL Server connector with appropriate credentials

#### Snowflake BI Tools
```
Account: FISDEV.us-east-1
Warehouse: CAPITAL_MARKETS_IDR__WH
Database: ETLCONFIGURATION_PEAPI
Schema: LANDING
```

### Key Reporting Tables
Focus on these tables for BI reporting:
- **vwBIPEJobCores_PEAPI**: Core job performance metrics
- **vwBIPEDomain_Workspace**: Domain and workspace analysis
- **JobHistory**: Detailed job execution history
- **results_space_usage**: Storage utilization reports

## Appendices

### Appendix A: File Structure
```
DataLoading/
├── RunScripts/
│   ├── DataLoadingConfig.py
│   ├── DataLoading_toSQL.py
│   ├── DataLoading_toSnowflake.py
│   ├── Run.cmd
│   └── Modules/
│       ├── DataLoadingFunctions.py
│       ├── PEAPI_Extract.ps1
│       └── EncryptPassword.py
├── Sources/
│   ├── JobHistory.csv
│   ├── Workspaces.csv
│   └── [other CSV files]
├── LoadLog/
│   ├── [date-stamped log files]
├── Input/
│   ├── PEDomain.txt
│   ├── ExtractionStartTime.txt
│   └── CostPerHour.txt
└── DB_OBJ/
    ├── Landing Schema DDL.sql
    ├── Snowflake Create Table DDL.sql
    └── Snowflake Create View DDL.sql
```

### Appendix B: Error Codes and Messages
Common error patterns and their meanings:

- **"cannot connect to SQL database"**: Database connection failure
- **"Errors occur when executing mergeQuery"**: Data loading SQL error
- **"Connect-ProphetSessionWinAuthent failed"**: PE API authentication error
- **"File not found"**: Missing input files or incorrect paths

### Appendix C: Sample Queries

#### Check Load Status
```sql
SELECT LoadId, TableName, Started, Finished, LoadStatus
FROM ETLLoad
ORDER BY Started DESC;
```

#### View Recent Job History
```sql
SELECT TOP 100 *
FROM JobHistory
ORDER BY LoadedDateTime DESC;
```

#### Monitor Data Freshness
```sql
SELECT TableName, MAX(LoadedDateTime) as LastLoaded
FROM (
    SELECT 'JobHistory' as TableName, MAX(LoadedDateTime) as LoadedDateTime FROM JobHistory
    UNION ALL
    SELECT 'Workspaces' as TableName, MAX(LoadedDateTime) as LoadedDateTime FROM Workspaces
) t
GROUP BY TableName;
```

---

*For technical support or additional configuration assistance, contact the BI team or system administrator.*

**Document Version**: 1.0
**Last Updated**: January 2025
**Prepared by**: BI Dashboard Team
