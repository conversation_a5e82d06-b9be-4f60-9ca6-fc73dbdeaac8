<?xml version="1.0" encoding="UTF-8"?>
<mapping xmlns="http://schemas.idr.com/mapping/v1.0" 
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://schemas.idr.com/mapping/v1.0 mapping.xsd">
    
    <metadata>
        <name>{{ mapping_spec.mapping_id }}</name>
        <description>Customer data mapping from CRM to IDR format</description>
        <version>1.0</version>
        <created>{{ mapping_spec.created_date }}</created>
        <author>LLM XML Mapping Generator</author>
    </metadata>
    
    <source>
        <schema>{{ mapping_spec.source_schema }}</schema>
        <namespace>http://schemas.crm.com/customer/v1.0</namespace>
    </source>
    
    <target>
        <schema>{{ mapping_spec.target_schema }}</schema>
        <namespace>http://schemas.idr.com/customer/v1.0</namespace>
    </target>
    
    <transformations>
        {% for mapping in mapping_spec.mappings %}
        <transform>
            <source_path>{{ mapping.source_path }}</source_path>
            <target_path>{{ mapping.target_path }}</target_path>
            
            {% if mapping.transformation %}
            <function>{{ mapping.transformation }}</function>
            {% endif %}
            
            {% if mapping.conditions %}
            <conditions>
                {% for condition in mapping.conditions %}
                <condition>{{ condition }}</condition>
                {% endfor %}
            </conditions>
            {% endif %}
            
            {% if mapping.default_value %}
            <default_value>{{ mapping.default_value }}</default_value>
            {% endif %}
            
            <data_type>{{ mapping.data_type | default('string') }}</data_type>
            <required>{{ mapping.required | default('false') }}</required>
        </transform>
        {% endfor %}
    </transformations>
    
    <validation_rules>
        {% for rule in mapping_spec.global_rules %}
        <rule>{{ rule }}</rule>
        {% endfor %}
    </validation_rules>
    
    <error_handling>
        <strategy>{{ mapping_spec.error_handling | default('log_and_continue') }}</strategy>
        <max_errors>{{ mapping_spec.max_errors | default('100') }}</max_errors>
        <error_output>{{ mapping_spec.error_output | default('error_log.xml') }}</error_output>
    </error_handling>
    
</mapping>
