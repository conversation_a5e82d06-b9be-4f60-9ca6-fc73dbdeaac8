# LLM-Powered XML Mapping Generator Solution

## Overview

This solution leverages Large Language Models (LLMs) to automatically generate XML mapping files based on natural language prompts from end users. It integrates with your existing IDR (Integrated Data Repository) infrastructure and proprietary XML mapping templates.

## Architecture Components

### 1. **Core Engine** (`llm_xml_mapping_generator.py`)
- **LLM Integration**: Supports OpenAI, Azure OpenAI, and local models
- **Template Library**: Manages proprietary XML mapping templates
- **Schema Validator**: Validates generated mappings against XSD schemas
- **Mapping Generator**: Creates XML mappings from specifications

### 2. **Web Interface** (`web_interface.py`)
- **Flask-based API**: RESTful endpoints for mapping generation
- **Real-time Updates**: WebSocket support for streaming generation
- **File Management**: Upload, download, and validation capabilities
- **Chat Interface**: Interactive AI assistant for guidance

### 3. **Template System**
- **Jinja2 Templates**: Flexible XML template engine
- **Metadata-driven**: YAML metadata for template matching
- **Business Domain Mapping**: Domain-specific template categorization

## Key Features

### 🤖 **Natural Language Processing**
- Convert user prompts into structured mapping requirements
- Extract source/target fields, transformations, and business rules
- Support for complex mapping scenarios

### 📋 **Template Matching**
- Intelligent template selection based on requirements
- Similarity scoring using field overlap and business domain
- Fallback to generic templates when needed

### ✅ **Validation & Quality Assurance**
- XML schema validation (XSD)
- XPath expression validation
- Data type compatibility checking
- Business rule validation

### 🔄 **Real-time Generation**
- Streaming generation with progress updates
- WebSocket-based real-time communication
- Parallel processing for multiple requests

## Installation & Setup

### Prerequisites
```bash
# Python 3.8+
pip install flask flask-socketio openai pyyaml jinja2 lxml xmlschema
```

### Configuration
1. **Update `mapping_config.yaml`**:
   ```yaml
   llm_provider: "openai"
   llm_model: "gpt-4"
   openai_api_key: "your-api-key"
   template_path: "./templates"
   schema_path: "./schemas"
   ```

2. **Set up directory structure**:
   ```
   project/
   ├── llm_xml_mapping_generator.py
   ├── web_interface.py
   ├── mapping_config.yaml
   ├── templates/
   │   ├── customer_mapping_template.xml
   │   ├── customer_mapping_template.yaml
   │   └── [other templates]
   ├── schemas/
   │   ├── source_schemas/
   │   └── target_schemas/
   └── generated_mappings/
   ```

### Running the Application
```bash
python web_interface.py
```
Access the web interface at: `http://localhost:5000`

## Usage Examples

### Example 1: Customer Data Mapping
**User Prompt**: 
> "Map customer data from our CRM system to IDR format. Include address normalization, email validation, and phone number formatting. Source has fields: cust_id, fname, lname, email_addr, phone_num, street, city, state, zip. Target should follow IDR customer schema."

**Generated Output**:
- Automatically selects customer mapping template
- Creates field mappings with appropriate transformations
- Includes validation rules for email and phone
- Generates complete XML mapping file

### Example 2: Financial Transaction Mapping
**User Prompt**:
> "Create mapping for financial transactions from legacy system to new IDR format. Need to convert currency codes, validate amounts, and map transaction types. Include audit trail fields."

**Generated Output**:
- Identifies financial domain template
- Maps transaction fields with currency conversion
- Adds validation for monetary amounts
- Includes audit and compliance fields

## Integration with Existing Systems

### 1. **IDR Integration**
```python
# Example integration with your IDR system
from llm_xml_mapping_generator import LLMXMLMappingGenerator

generator = LLMXMLMappingGenerator()

# Generate mapping
request = MappingRequest(
    user_prompt="Map PE job data to IDR format",
    source_schema="pe_job_schema.xsd",
    target_schema="idr_job_schema.xsd",
    business_context="Prophet Enterprise job tracking"
)

mapping = generator.generate_mapping(request)

# Deploy to IDR
deploy_to_idr(mapping.xml_content)
```

### 2. **Snowflake Integration**
```python
# Integration with your existing Snowflake setup
def deploy_mapping_to_snowflake(mapping: XMLMapping):
    session = Session.builder.configs({
        "account": config.SnowflakeAccount,
        "warehouse": config.SnowflakeWarehouse,
        "database": config.SnowflakeDatabase
    }).create()
    
    # Store mapping metadata
    session.sql(f"""
        INSERT INTO MAPPING_REGISTRY 
        (mapping_id, xml_content, created_date, status)
        VALUES ('{mapping.mapping_id}', '{mapping.xml_content}', 
                CURRENT_TIMESTAMP(), 'ACTIVE')
    """).collect()
```

## Advanced Features

### 1. **Custom Transformation Functions**
```python
# Add custom transformation functions
def register_custom_transformations():
    transformations = {
        'pe_domain_normalize': lambda x: x.replace('https://', '').split('/')[0],
        'job_status_standardize': lambda x: 'COMPLETED' if x == 'Finished' else x.upper(),
        'duration_calculate': lambda start, end: (end - start).total_seconds()
    }
    return transformations
```

### 2. **Business Rule Engine**
```python
# Define business rules for validation
business_rules = {
    'customer_data': [
        'email_required_for_active_customers',
        'phone_format_validation',
        'address_completeness_check'
    ],
    'financial_data': [
        'amount_positive_validation',
        'currency_code_validation',
        'transaction_date_validation'
    ]
}
```

### 3. **Template Versioning**
```yaml
# Template metadata with versioning
name: "Customer Mapping Template"
version: "2.1"
compatibility:
  min_version: "2.0"
  max_version: "2.9"
changelog:
  - version: "2.1"
    changes: ["Added GDPR compliance fields", "Enhanced address validation"]
  - version: "2.0"
    changes: ["Major restructure for IDR v2.0 compatibility"]
```

## API Reference

### REST Endpoints

#### Generate Mapping
```http
POST /api/generate-mapping
Content-Type: application/json

{
  "prompt": "Map customer data with validation",
  "source_schema": "crm_customer.xsd",
  "target_schema": "idr_customer.xsd",
  "business_context": "Customer data integration",
  "transformation_rules": ["normalize_address", "validate_email"]
}
```

#### Validate Mapping
```http
POST /api/validate-mapping
Content-Type: application/json

{
  "xml_content": "<mapping>...</mapping>"
}
```

#### Download Mapping
```http
GET /api/download-mapping/{mapping_id}
```

### WebSocket Events

#### Real-time Generation
```javascript
// Client-side WebSocket usage
socket.emit('generate_mapping_stream', {
  prompt: "Create customer mapping",
  source_schema: "source.xsd",
  target_schema: "target.xsd"
});

socket.on('status', (data) => {
  console.log(`Progress: ${data.progress}% - ${data.message}`);
});

socket.on('mapping_complete', (data) => {
  console.log('Mapping generated:', data.mapping_id);
});
```

## Security Considerations

### 1. **Input Validation**
- Sanitize all user inputs
- Validate XML structure before processing
- Prevent XXE (XML External Entity) attacks

### 2. **API Security**
- Implement rate limiting
- Use authentication tokens
- Validate file uploads

### 3. **Data Protection**
- Encrypt sensitive configuration data
- Secure API keys and credentials
- Implement audit logging

## Performance Optimization

### 1. **Caching Strategy**
```python
# Template and schema caching
@lru_cache(maxsize=100)
def get_cached_template(template_name):
    return load_template(template_name)

@lru_cache(maxsize=50)
def get_cached_schema(schema_name):
    return load_schema(schema_name)
```

### 2. **Parallel Processing**
```python
# Concurrent mapping generation
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def generate_multiple_mappings(requests):
    with ThreadPoolExecutor(max_workers=5) as executor:
        tasks = [
            loop.run_in_executor(executor, generate_mapping, req)
            for req in requests
        ]
        return await asyncio.gather(*tasks)
```

## Monitoring & Maintenance

### 1. **Logging**
- Comprehensive logging for all operations
- Error tracking and alerting
- Performance metrics collection

### 2. **Health Checks**
```python
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'llm_connection': check_llm_connection(),
        'template_count': len(template_library.templates),
        'schema_count': len(schema_validator.schemas)
    }
```

### 3. **Metrics Dashboard**
- Generation success rate
- Average processing time
- Template usage statistics
- Error rate monitoring

## Troubleshooting

### Common Issues

1. **LLM Connection Errors**
   - Verify API keys and endpoints
   - Check network connectivity
   - Monitor rate limits

2. **Template Matching Issues**
   - Review template metadata
   - Adjust similarity thresholds
   - Add more specific templates

3. **Validation Failures**
   - Check XSD schema compatibility
   - Verify XPath expressions
   - Review data type mappings

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Test individual components
generator = LLMXMLMappingGenerator()
generator.logger.setLevel(logging.DEBUG)
```

## Future Enhancements

1. **Machine Learning Integration**
   - Learn from user feedback
   - Improve template matching accuracy
   - Automated template generation

2. **Advanced UI Features**
   - Visual mapping designer
   - Drag-and-drop interface
   - Real-time preview

3. **Enterprise Features**
   - Multi-tenant support
   - Role-based access control
   - Integration with enterprise systems

---

This solution provides a comprehensive framework for LLM-powered XML mapping generation that can be customized and extended based on your specific IDR requirements and business needs.
