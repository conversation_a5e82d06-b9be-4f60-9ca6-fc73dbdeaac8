from datetime import date, datetime
import pandas as pd
import pyodbc
import os
from colorama import Fore
from cryptography.fernet import Fernet
import Modules.DataLoadingFunctions as fn
import DataLoadingConfig as config
import snowflake.connector
import warnings


warnings.filterwarnings(
    action='ignore',
    category=UserWarning,
    module='snowflake.connector'
)


#Connect Snowflake
def get_snowflake_connection():
    try:
        # If using encrypted password
        if config.SnowflakePassword_encrypted:
            password = Fernet(config.SnowflakePassword_encryptKey).decrypt(
                config.SnowflakePassword_encrypted).decode("utf-8")
        else:
            password = config.SnowflakePassword

        # Create connection

        user=config.SnowflakeUsername,
        account=config.SnowflakeAccount,
        warehouse=config.SnowflakeWarehouse,
        database=config.SnowflakeDatabase,
        schema=config.SnowflakeSchema,
        role=config.SnowflakeRole

        conn = snowflake.connector.connect(
            user=user,
            password=password,
            account=account,
            warehouse=warehouse,
            database=database,
            schema=schema,
            role=role
        )

        # conn = snowflake.connector.connect(
        #     user=config.SnowflakeUsername,
        #     password=password,
        #     account=config.SnowflakeAccount,
        #     warehouse=config.SnowflakeWarehouse,
        #     database=config.SnowflakeDatabase,
        #     schema=config.SnowflakeSchema,
        #     role=config.SnowflakeRole
        # )
        
        print(f"{Fore.GREEN}Successfully connected to Snowflake{Fore.RESET}")
        return conn
    
    except Exception as e:
        print(f"{Fore.RED}Error connecting to Snowflake: {str(e)}{Fore.RESET}")
        raise SystemExit()

def execute_query(conn, query, params=None):
    """Execute a query and return results"""
    try:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        results = cursor.fetchall()
        cursor.close()
        return results
    
    except Exception as e:
        print(f"{Fore.RED}Error executing query: {str(e)}{Fore.RESET}")
        raise

def load_dataframe_to_snowflake(conn, df, table_name, schema=None):
    """Load a pandas DataFrame to Snowflake table"""
    try:
        # If schema is provided, use it, otherwise use default
        full_table_name = f"{schema}.{table_name}" if schema else table_name
        
        # Write the dataframe to Snowflake
        success, nchunks, nrows, _ = write_pandas(
            conn=conn,
            df=df,
            table_name=full_table_name,
            quote_identifiers=False
        )
        
        print(f"{Fore.GREEN}Successfully loaded {nrows} rows to {full_table_name}{Fore.RESET}")
        return success
    
    except Exception as e:
        print(f"{Fore.RED}Error loading data to Snowflake: {str(e)}{Fore.RESET}")
        raise

def main():
    # Example usage
    conn = get_snowflake_connection()
    
    # Example query
    query = "SELECT CURRENT_WAREHOUSE(), CURRENT_DATABASE(), CURRENT_SCHEMA()"
    results = execute_query(conn, query)
    print("Current session details:", results)
    
    # Example data loading
    try:
        # Create sample DataFrame
        df = pd.DataFrame({
            'col1': [1, 2, 3],
            'col2': ['a', 'b', 'c']
        })
        
        # Load to Snowflake
        load_dataframe_to_snowflake(conn, df, 'SAMPLE_TABLE')
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()