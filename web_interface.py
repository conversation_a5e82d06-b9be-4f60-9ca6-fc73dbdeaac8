"""
Web Interface for LLM-Powered XML Mapping Generator
Flask-based web application for user interaction
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_socketio import Socket<PERSON>, emit
import json
import os
from datetime import datetime
import uuid
from llm_xml_mapping_generator import LL<PERSON><PERSON><PERSON>appingGenerator, MappingRequest
import logging

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize the mapping generator
mapping_generator = LLMXMLMappingGenerator()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.route('/')
def index():
    """Main page with mapping request form"""
    return render_template('index.html')

@app.route('/api/generate-mapping', methods=['POST'])
def generate_mapping():
    """API endpoint to generate XML mapping"""
    try:
        data = request.json
        
        # Create mapping request
        mapping_request = MappingRequest(
            user_prompt=data.get('prompt', ''),
            source_schema=data.get('source_schema', ''),
            target_schema=data.get('target_schema', ''),
            transformation_rules=data.get('transformation_rules', []),
            business_context=data.get('business_context', '')
        )
        
        # Generate mapping
        mapping = mapping_generator.generate_mapping(mapping_request)
        
        # Save generated mapping
        mapping_id = str(uuid.uuid4())
        save_mapping(mapping_id, mapping)
        
        return jsonify({
            'success': True,
            'mapping_id': mapping_id,
            'preview': mapping.xml_content[:500] + '...' if len(mapping.xml_content) > 500 else mapping.xml_content,
            'source_elements': mapping.source_elements,
            'target_elements': mapping.target_elements
        })
        
    except Exception as e:
        logger.error(f"Error generating mapping: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/download-mapping/<mapping_id>')
def download_mapping(mapping_id):
    """Download generated XML mapping file"""
    try:
        mapping_file = f"generated_mappings/{mapping_id}.xml"
        if os.path.exists(mapping_file):
            return send_file(mapping_file, as_attachment=True, download_name=f"mapping_{mapping_id}.xml")
        else:
            return jsonify({'error': 'Mapping not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/validate-mapping', methods=['POST'])
def validate_mapping():
    """Validate XML mapping"""
    try:
        data = request.json
        xml_content = data.get('xml_content', '')
        
        # Perform validation
        validation_result = mapping_generator.schema_validator.validate_mapping_content(xml_content)
        
        return jsonify({
            'is_valid': validation_result.is_valid,
            'errors': validation_result.errors,
            'warnings': validation_result.warnings
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/templates')
def get_templates():
    """Get available mapping templates"""
    try:
        templates = []
        for name, template_data in mapping_generator.template_library.templates.items():
            templates.append({
                'name': name,
                'description': template_data['metadata'].get('description', ''),
                'complexity': template_data['metadata'].get('complexity', 'medium'),
                'business_domain': template_data['metadata'].get('business_domain', '')
            })
        
        return jsonify({'templates': templates})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/schemas')
def get_schemas():
    """Get available schemas"""
    try:
        schemas = list(mapping_generator.schema_validator.schemas.keys())
        return jsonify({'schemas': schemas})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('generate_mapping_stream')
def handle_streaming_generation(data):
    """Handle streaming mapping generation with real-time updates"""
    try:
        emit('status', {'message': 'Starting mapping generation...', 'progress': 10})
        
        # Create mapping request
        mapping_request = MappingRequest(
            user_prompt=data.get('prompt', ''),
            source_schema=data.get('source_schema', ''),
            target_schema=data.get('target_schema', ''),
            transformation_rules=data.get('transformation_rules', []),
            business_context=data.get('business_context', '')
        )
        
        emit('status', {'message': 'Analyzing requirements...', 'progress': 30})
        
        # Generate mapping with progress updates
        mapping = mapping_generator.generate_mapping(mapping_request)
        
        emit('status', {'message': 'Validating mapping...', 'progress': 80})
        
        # Save mapping
        mapping_id = str(uuid.uuid4())
        save_mapping(mapping_id, mapping)
        
        emit('status', {'message': 'Mapping generated successfully!', 'progress': 100})
        emit('mapping_complete', {
            'mapping_id': mapping_id,
            'xml_content': mapping.xml_content,
            'source_elements': mapping.source_elements,
            'target_elements': mapping.target_elements
        })
        
    except Exception as e:
        emit('error', {'message': str(e)})

def save_mapping(mapping_id: str, mapping):
    """Save generated mapping to file"""
    os.makedirs('generated_mappings', exist_ok=True)
    
    # Save XML content
    with open(f'generated_mappings/{mapping_id}.xml', 'w') as f:
        f.write(mapping.xml_content)
    
    # Save metadata
    metadata = {
        'mapping_id': mapping.mapping_id,
        'generated_at': datetime.now().isoformat(),
        'source_elements': mapping.source_elements,
        'target_elements': mapping.target_elements,
        'transformations': mapping.transformations
    }
    
    with open(f'generated_mappings/{mapping_id}_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('generated_mappings', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('schemas', exist_ok=True)
    
    # Run the application
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
