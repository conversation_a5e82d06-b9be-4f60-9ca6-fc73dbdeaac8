from datetime import date, datetime
import pandas as pd
import pyodbc
import os
from colorama import Fore
import subprocess
import DataLoadingConfig as config

def writeLog(logFile,msg_type,msg_log):
    if (msg_type == 'Error'):
        print (Fore.RED,msg_log)
    elif(msg_type == 'Success'):
        print(Fore.YELLOW,msg_log)
    else:
        print(Fore.WHITE,msg_log)
    lines = ['', "{0} - {1} - {2:9}{3}".format(datetime.today().strftime("%H:%M:%S"), os.getlogin(), msg_type, msg_log)]
    with open(logFile , 'a') as f:
        f.writelines('\n'.join(lines))

def writeLogToDB(DbConnect,LoadId,TableName,LogType,LogEvent,LogInfo):
    LogInfo = str(LogInfo).replace('\'','\'\'')
    #split LogInfo if length > 2000
    LogInfo_split = [LogInfo[i:i+2000] for i in range(0,len(LogInfo),2000)]
    for Log in LogInfo_split:
        writeLogQuery = ('insert into LoadLog(LoadId,TableName,Timestamp,TimestampLocal,LogType,LogEvent,LogInfo) values (' + str(LoadId) + ',\'' +
                TableName + '\', SYSDATETIME(),SYSDATETIMEOFFSET(),\'' + LogType + '\',\'' + LogEvent + '\',\'' + Log + '\')')
        #print(writeLogQuery)
        cursor=DbConnect.cursor()
        cursor.execute(writeLogQuery)
        DbConnect.commit()

def getLoadId (DbConnect):
    sqlQuery = 'select max(LoadId) from ETLLoad'
    cursor = DbConnect.cursor()
    cursor.execute(sqlQuery)
    res = cursor.fetchone()
    cursor.close()
    if res is None:
        return None
    else:
        return res[0] 
    
def getColumn(DbConnect,TableName):
    sqlQuery = 'select col.name from sys.all_columns col inner join sys.all_objects tab on col.object_id=tab.object_id where tab.name=\''+TableName+'\' order by column_id'
    cursor = DbConnect.cursor()
    cursor.execute(sqlQuery)
    res = cursor.fetchall()
    cursor.close()
    colNames=[]
    for i in range(len(res)):
        colNames.append(res[i][0])
    return colNames
    
def getPK(DbConnect,TableName):
    sqlQuery = ('select col.name from sys.all_columns col inner join sys.all_objects tab on col.object_id=tab.object_id ' 
        + 'inner join sys.index_columns ind_c on ind_c.object_id = col.object_id and ind_c.column_id=col.column_id '
        + 'inner join sys.indexes ind on ind.object_id=col.object_id and ind.index_id=ind_c.index_id '
        + 'where tab.name=\''+TableName+'\' and ind.is_primary_key=1 '
        + 'order by col.column_id')
    cursor = DbConnect.cursor()
    cursor.execute(sqlQuery)
    res = cursor.fetchall()
    cursor.close()
    if res is None:
        return None

    PKNames=[]
    for i in range(len(res)):
        PKNames.append(res[i][0])
    return PKNames    

def createFormatFile(connectString,formatFilePath,TableName):
    DbName = connectString.split("Database=")[1].split(';')[0]
    ServerName = connectString.split("Server=")[1].split(';')[0]
    formatFile = formatFilePath + '\\' + TableName + '.fmt'
    bcp_cmd = 'bcp.exe ' + DbName + '.dbo.' + TableName + ' format nul -c -f ' + '\"'+ formatFile + '\"  -S \"'+ ServerName +'\" -t, -T'
    nb_row=0
    rows=[]
    try:
        #create format file from table format in database
        subprocess.call(bcp_cmd)
        #update format file by removing loadedDateTime row
        with open(formatFile,'r') as f:
            for i,row in enumerate(f):
                if (i==0):
                    rows.append(row)
                elif (i==1):
                    nb_row = int(row)
                    rows.append(str(nb_row-1)+'\n')
                elif (i==nb_row):
                    rows.append(row.replace(',','\\r\\n'))
                elif (i==nb_row+1):
                    continue #skip loadedDateTime row
                else:
                    rows.append(row)
        with open(formatFile,'w') as f:
            f.writelines(rows)
        return(formatFile)
    except Exception as ex:
        print(str(ex))

def loadData (DbConnect,LoadId,sourceFile,logPath):
    targetTableName = sourceFile[:sourceFile.find('.')]
    errorFile = logPath + "\\error" + sourceFile
    logFile = logPath + '\\' + targetTableName + date.today().strftime('%Y%m%d') + '.txt'
    cursor=DbConnect.cursor()
    separator = ','
    
    #data loading methode: update insert
    all_col = getColumn(DbConnect,targetTableName)
    PK = getPK(DbConnect,targetTableName)
    if (PK is None) :
        Keys = all_col[0] # if no PK available then the first column of target table is considered as a key for update\insert data
        Columns = all_col[1:]
    else:
        Keys=PK
        Columns = [item for item in all_col if item not in PK]
    all_col_string = ','.join(all_col)
    
    keyStringTemp=[]
    keyString=[]
    for k in Keys:
        keyStringTemp.append("trg." + k + "=src." + k)
    keyString = ' and '.join(keyStringTemp)
    
    colUpdateStringTemp = []
    srcValueStringTemp = []
    for c in Columns:
        colUpdateStringTemp.append("trg." + c + "=src." + c)
    colUpdateString = ','.join(colUpdateStringTemp)
    for h in all_col:
        srcValueStringTemp.append("src." + h)
    srcValueString = ','.join(srcValueStringTemp)
    with open (config.sourcesPath + "\\"+sourceFile,'r') as readSrc:
        for i,row in enumerate(readSrc):
            if (i==0):
                headerString = row.replace('\n','').replace('"','') + ',LoadId, LoadedDateTime'
            else: # construct merge query
                rowValue = row.replace('\n','').replace('\'','\'\'').replace('"','\'').replace(',,',',null,') + ',' + str(LoadId) + ',SYSDATETIME()'
                mergeQuery = ("merge into " + targetTableName + " as trg using (values( " 
                              + rowValue + ")) as src( " + headerString + ") on (" + keyString
                              + ") when matched then update set " + colUpdateString 
                              + " when not matched then insert (" + all_col_string + ") values (" + srcValueString + ");")
                #writeLog(logFile,'Info','Execute mergeQuery: ' + mergeQuery)
                #writeLogToDB(DbConnect,LoadId,targetTableName,'Info','loadData','Execute mergeQuery: ' + mergeQuery)
                try:
                    cursor.execute(mergeQuery)
                    DbConnect.commit()
                    #writeLog(logFile,'Success','updated table '+targetTableName)
                    #writeLogToDB(DbConnect,LoadId,targetTableName,'Success','loadData','updated table '+targetTableName)
                except Exception as ex:
                    writeLog(logFile,'Error','Errors occur when executing mergeQuery: ' + mergeQuery + ' ' + str(ex))
                    writeLogToDB(DbConnect,LoadId,targetTableName,'Error','loadData','Errors occur when executing mergeQuery: ' + mergeQuery + ' ' + str(ex))
                    updateLoadFailed = 'Update ETLLoad set finished = SYSDATETIME(), finishedlocal = SYSDATETIMEOFFSET(), LoadStatus = \'Failed\' where LoadId=' + str(LoadId)
                    cursor.execute(updateLoadFailed)
                    DbConnect.commit()
                    raise SystemExit()
    
    # count number of rows that have been loaded or updated
    countQuery = 'select count(*) from '+targetTableName+' where LoadId=' + str(LoadId)
    cursor.execute(countQuery)
    nb_rows = cursor.fetchone()
    writeLog(logFile,'Info', str(nb_rows) + 'rows updated in table '+targetTableName)
    writeLogToDB(DbConnect,LoadId,targetTableName,'Info','loadData',str(nb_rows) + 'rows updated in table '+targetTableName)
    
    # update ETLLoad table with completed status
    updateLoadCompleted = 'Update ETLLoad set finished = SYSDATETIME(), finishedlocal = SYSDATETIMEOFFSET(), LoadStatus = \'Completed\' where LoadId=' + str(LoadId)
    cursor.execute(updateLoadCompleted)
    DbConnect.commit()
    
    cursor.close()

def get_folder_size(folder_path):
    total_size = 0
    if not os.path.exists(folder_path):
        return None
        
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for f in filenames:
            fp = os.path.join(dirpath,f)
            total_size += os.path.getsize(fp)
    return total_size

def load_data_snowflake(session, source_file, load_id):
    """Load CSV file into Snowflake table with merge operation"""
    try:
        target_table_name = source_file[:source_file.find('.')]
        
        # Read CSV file into Snowpark DataFrame
        snow_df = session.read.option("header", True)\
            .option("inferSchema", True)\
            .csv(f"{config.sourcesPath}/{source_file}")
        
        # Add LoadId and LoadedDateTime columns
        snow_df = snow_df.with_column("LOADID", load_id)\
            .with_column("LOADEDDATETIME", session.sql("SELECT CURRENT_TIMESTAMP()").collect()[0][0])
        
        # Get primary keys and columns
        pk_cols = get_primary_keys_snowflake(session, target_table_name)
        if not pk_cols:
            # If no PK, use first column as key
            all_cols = snow_df.columns
            pk_cols = [all_cols[0]]
            update_cols = all_cols[1:]
        else:
            update_cols = [col for col in snow_df.columns if col.upper() not in [pk.upper() for pk in pk_cols]]
        
        # Construct merge statement
        merge_query = f"""
        MERGE INTO {target_table_name} AS trg
        USING (SELECT * FROM {snow_df.create_or_replace_temp_view("TEMP_SOURCE_VIEW")}) AS src
        ON {' AND '.join([f'trg.{pk} = src.{pk}' for pk in pk_cols])}
        WHEN MATCHED THEN UPDATE SET
            {', '.join([f'trg.{col} = src.{col}' for col in update_cols])},
            trg.LOADID = src.LOADID,
            trg.LOADEDDATETIME = src.LOADEDDATETIME
        WHEN NOT MATCHED THEN INSERT
            ({', '.join(snow_df.columns)})
        VALUES
            ({', '.join([f'src.{col}' for col in snow_df.columns])})
        """
        
        # Execute merge
        result = session.sql(merge_query).collect()
        
        # Log results
        rows_updated = result[0]['number of rows updated']
        rows_inserted = result[0]['number of rows inserted']
        
        # Update ETLLoad status
        update_etl_load_status(session, load_id, target_table_name, 'Completed', 
                             f"Merged {rows_updated} rows, inserted {rows_inserted} rows")
        
        return rows_updated + rows_inserted
        
    except Exception as ex:
        error_msg = f"Error loading data: {str(ex)}"
        update_etl_load_status(session, load_id, target_table_name, 'Failed', error_msg)
        raise

def get_primary_keys_snowflake(session, table_name):
    """Get primary key columns for a Snowflake table"""
    pk_query = f"""
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE TABLE_NAME = '{table_name.upper()}'
    AND CONSTRAINT_NAME LIKE 'PK_%'
    ORDER BY ORDINAL_POSITION
    """
    
    result = session.sql(pk_query).collect()
    return [row['COLUMN_NAME'] for row in result] if result else None

def update_etl_load_status(session, load_id, table_name, status, message):
    """Update ETLLoad table with load status"""
    update_query = f"""
    UPDATE ETLLoad 
    SET FINISHED = CURRENT_TIMESTAMP(),
        FINISHEDLOCAL = CURRENT_TIMESTAMP(),
        LOADSTATUS = '{status}',
        LOADMESSAGE = '{message}'
    WHERE LOADID = {load_id}
    """
    session.sql(update_query).collect()