"""
Example usage of the LLM XML Mapping Generator
Demonstrates how to integrate with your existing IDR system
"""

import os
import sys
from datetime import datetime
from llm_xml_mapping_generator import LLMXMLMappingGenerator, MappingRequest

def setup_example_environment():
    """Set up example templates and schemas for demonstration"""
    
    # Create directories if they don't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('schemas', exist_ok=True)
    os.makedirs('generated_mappings', exist_ok=True)
    
    print("✅ Example environment set up successfully")

def example_pe_to_idr_mapping():
    """Example: Map Prophet Enterprise job data to IDR format"""
    
    print("\n🔄 Generating PE to IDR mapping...")
    
    # Initialize the generator
    generator = LLMXMLMappingGenerator()
    
    # Create mapping request for PE job data
    request = MappingRequest(
        user_prompt="""
        Map Prophet Enterprise job execution data to IDR format for business intelligence reporting.
        
        Source data includes:
        - JobHistoryId: Unique job identifier
        - Job_Name: Name of the executed job
        - Workspace: PE workspace name
        - SubmittedBy: User who submitted the job
        - FinishedJobState: Job completion status (Completed, Failed, Cancelled)
        - RunStarted: Job start timestamp
        - RunFinished: Job completion timestamp
        - MachineGroup: Execution machine group
        - RunNumbers: Number of runs executed
        
        Target IDR format should include:
        - Standardized job identifiers
        - Normalized timestamps in ISO format
        - Calculated duration fields
        - Standardized status codes
        - User information with domain normalization
        - Performance metrics
        
        Apply these transformations:
        - Convert timestamps to ISO 8601 format
        - Calculate job duration in seconds
        - Normalize user names (remove domain info)
        - Standardize status codes
        - Add data quality flags
        """,
        source_schema="pe_job_history.xsd",
        target_schema="idr_job_metrics.xsd",
        transformation_rules=[
            "normalize_timestamps",
            "calculate_duration",
            "standardize_status",
            "normalize_user_info",
            "add_quality_flags"
        ],
        business_context="Prophet Enterprise job tracking and performance analytics for IDR reporting"
    )
    
    try:
        # Generate the mapping
        mapping = generator.generate_mapping(request)
        
        print(f"✅ Mapping generated successfully!")
        print(f"   Mapping ID: {mapping.mapping_id}")
        print(f"   Source Elements: {len(mapping.source_elements)}")
        print(f"   Target Elements: {len(mapping.target_elements)}")
        
        # Save the mapping
        output_file = f"generated_mappings/pe_to_idr_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        with open(output_file, 'w') as f:
            f.write(mapping.xml_content)
        
        print(f"   Saved to: {output_file}")
        
        # Display preview
        print(f"\n📋 XML Preview (first 500 chars):")
        print(mapping.xml_content[:500] + "..." if len(mapping.xml_content) > 500 else mapping.xml_content)
        
        return mapping
        
    except Exception as e:
        print(f"❌ Error generating mapping: {str(e)}")
        return None

def example_customer_data_mapping():
    """Example: Map customer data from CRM to IDR format"""
    
    print("\n🔄 Generating Customer Data mapping...")
    
    generator = LLMXMLMappingGenerator()
    
    request = MappingRequest(
        user_prompt="""
        Create a mapping for customer data integration from our CRM system to IDR format.
        
        Source CRM fields:
        - cust_id: Customer unique identifier
        - fname, lname: Customer first and last name
        - email_addr: Email address
        - phone_num: Phone number (various formats)
        - street_addr, city, state_code, zip_code: Address components
        - country: Country name
        - created_dt: Account creation date
        - last_modified: Last modification timestamp
        - status: Account status (Active, Inactive, Suspended)
        
        Target IDR requirements:
        - Standardized customer identifier
        - Full name concatenation
        - Validated email format
        - Standardized phone format (E.164)
        - Normalized address with geocoding
        - ISO country codes
        - ISO 8601 timestamps
        - Standardized status codes
        - Data quality scores
        
        Business rules:
        - Email is required for active customers
        - Phone number must be valid format
        - Address must be complete for billing customers
        - Country codes must be ISO 3166-1 alpha-2
        """,
        source_schema="crm_customer.xsd",
        target_schema="idr_customer.xsd",
        transformation_rules=[
            "validate_email",
            "format_phone_e164",
            "normalize_address",
            "standardize_country_code",
            "calculate_data_quality_score"
        ],
        business_context="Customer master data management and analytics"
    )
    
    try:
        mapping = generator.generate_mapping(request)
        
        print(f"✅ Customer mapping generated!")
        print(f"   Mapping ID: {mapping.mapping_id}")
        
        # Save the mapping
        output_file = f"generated_mappings/customer_mapping_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        with open(output_file, 'w') as f:
            f.write(mapping.xml_content)
        
        print(f"   Saved to: {output_file}")
        
        return mapping
        
    except Exception as e:
        print(f"❌ Error generating customer mapping: {str(e)}")
        return None

def example_integration_with_existing_system():
    """Example: Integration with existing DataLoading system"""
    
    print("\n🔄 Demonstrating integration with existing DataLoading system...")
    
    # Simulate integration with your existing DataLoadingConfig
    try:
        import DataLoadingConfig as config
        
        print(f"✅ Found existing DataLoading configuration")
        print(f"   SQL Server: {config.SQLServer}")
        print(f"   Snowflake Account: {config.SnowflakeAccount}")
        print(f"   Snowflake Warehouse: {config.SnowflakeWarehouse}")
        
        # Example: Generate mapping for existing data sources
        generator = LLMXMLMappingGenerator()
        
        # Check existing CSV files in Sources directory
        sources_path = getattr(config, 'sourcesPath', '../Sources')
        if os.path.exists(sources_path):
            csv_files = [f for f in os.listdir(sources_path) if f.endswith('.csv')]
            print(f"   Found {len(csv_files)} CSV files in Sources directory")
            
            for csv_file in csv_files[:3]:  # Process first 3 files as example
                table_name = csv_file.replace('.csv', '')
                
                request = MappingRequest(
                    user_prompt=f"""
                    Create an XML mapping for {table_name} data from Prophet Enterprise to IDR format.
                    This data is extracted via PE API and needs to be transformed for business intelligence reporting.
                    Include standard transformations for timestamps, user normalization, and data quality validation.
                    """,
                    source_schema=f"pe_{table_name.lower()}.xsd",
                    target_schema=f"idr_{table_name.lower()}.xsd",
                    transformation_rules=["normalize_timestamps", "standardize_status", "add_quality_flags"],
                    business_context=f"Prophet Enterprise {table_name} data integration for BI reporting"
                )
                
                try:
                    mapping = generator.generate_mapping(request)
                    output_file = f"generated_mappings/{table_name}_mapping.xml"
                    
                    with open(output_file, 'w') as f:
                        f.write(mapping.xml_content)
                    
                    print(f"   ✅ Generated mapping for {table_name}")
                    
                except Exception as e:
                    print(f"   ❌ Failed to generate mapping for {table_name}: {str(e)}")
        
    except ImportError:
        print("   ℹ️  DataLoadingConfig not found - this is normal for standalone demo")
        print("   ℹ️  In production, this would integrate with your existing configuration")

def example_batch_mapping_generation():
    """Example: Generate multiple mappings in batch"""
    
    print("\n🔄 Generating multiple mappings in batch...")
    
    # Define multiple mapping requirements
    mapping_requests = [
        {
            "name": "JobHistory",
            "prompt": "Map PE job execution history with performance metrics",
            "transformations": ["normalize_timestamps", "calculate_duration", "standardize_status"]
        },
        {
            "name": "Workspaces", 
            "prompt": "Map PE workspace configuration and metadata",
            "transformations": ["normalize_metadata", "standardize_permissions", "add_audit_fields"]
        },
        {
            "name": "MachineJobHistory",
            "prompt": "Map machine-specific job execution data with resource utilization",
            "transformations": ["normalize_machine_info", "calculate_resource_usage", "add_performance_metrics"]
        }
    ]
    
    generator = LLMXMLMappingGenerator()
    generated_mappings = []
    
    for req_config in mapping_requests:
        try:
            request = MappingRequest(
                user_prompt=req_config["prompt"],
                source_schema=f"pe_{req_config['name'].lower()}.xsd",
                target_schema=f"idr_{req_config['name'].lower()}.xsd",
                transformation_rules=req_config["transformations"],
                business_context="Prophet Enterprise data integration for IDR analytics"
            )
            
            mapping = generator.generate_mapping(request)
            
            # Save mapping
            output_file = f"generated_mappings/{req_config['name']}_batch_mapping.xml"
            with open(output_file, 'w') as f:
                f.write(mapping.xml_content)
            
            generated_mappings.append({
                'name': req_config['name'],
                'mapping_id': mapping.mapping_id,
                'file': output_file
            })
            
            print(f"   ✅ Generated {req_config['name']} mapping")
            
        except Exception as e:
            print(f"   ❌ Failed to generate {req_config['name']} mapping: {str(e)}")
    
    print(f"\n📊 Batch generation complete: {len(generated_mappings)} mappings created")
    return generated_mappings

def main():
    """Main demonstration function"""
    
    print("🚀 LLM XML Mapping Generator - Example Usage")
    print("=" * 50)
    
    # Set up environment
    setup_example_environment()
    
    # Run examples
    try:
        # Example 1: PE to IDR mapping
        pe_mapping = example_pe_to_idr_mapping()
        
        # Example 2: Customer data mapping
        customer_mapping = example_customer_data_mapping()
        
        # Example 3: Integration with existing system
        example_integration_with_existing_system()
        
        # Example 4: Batch generation
        batch_mappings = example_batch_mapping_generation()
        
        print("\n🎉 All examples completed successfully!")
        print("\nGenerated files:")
        for file in os.listdir('generated_mappings'):
            if file.endswith('.xml'):
                print(f"   📄 {file}")
        
    except Exception as e:
        print(f"\n❌ Error during execution: {str(e)}")
        print("💡 Make sure you have:")
        print("   - Valid OpenAI API key in mapping_config.yaml")
        print("   - Required dependencies installed (pip install -r requirements.txt)")
        print("   - Proper directory structure")

if __name__ == "__main__":
    main()
