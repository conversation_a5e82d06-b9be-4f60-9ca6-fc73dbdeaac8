name: "Customer Data Mapping Template"
description: "Template for mapping customer data from CRM systems to IDR format"
version: "1.0"
complexity: "medium"
business_domain: "customer_management"

# Fields commonly used in this template
fields:
  - "customer_id"
  - "first_name"
  - "last_name"
  - "email"
  - "phone"
  - "address"
  - "city"
  - "state"
  - "zip_code"
  - "country"
  - "created_date"
  - "modified_date"

# Common transformations for this template
transformations:
  - "normalize_address"
  - "validate_email"
  - "format_phone"
  - "standardize_country_code"
  - "validate_postal_code"

# Business rules typically applied
business_rules:
  - "email_required_for_active_customers"
  - "phone_format_validation"
  - "address_completeness_check"

# Data quality requirements
data_quality:
  - "no_duplicate_customer_ids"
  - "email_format_validation"
  - "required_field_validation"

# Use cases where this template applies
use_cases:
  - "CRM to IDR customer sync"
  - "Customer data consolidation"
  - "Customer master data management"
  - "Customer analytics preparation"

# Related templates
related_templates:
  - "contact_mapping_template"
  - "account_mapping_template"
  - "address_mapping_template"
